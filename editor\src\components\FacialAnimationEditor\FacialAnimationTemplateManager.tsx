/**
 * 面部动画模板管理组件
 * 用于管理面部动画模板
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  List, 
  Tag, 
  Space, 
  Input, 
  Select, 
  Modal, 
  Form, 
  message, 
  Tooltip, 
  Popconfirm,
  Upload,
  Divider,
  Empty,
  Collapse,
  Slider,
  Switch,
  InputNumber
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  ExportOutlined, 
  ImportOutlined, 
  CopyOutlined,
  EyeOutlined,
  SaveOutlined,
  UploadOutlined,
  DownloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
// 移除引擎直接导入
// 移除引擎直接导入
import { FacialAnimationPreview } from './FacialAnimationPreview';
import './FacialAnimationTemplateManager.less';

const { Panel } = Collapse;
const { Option } = Select;

/**
 * 面部动画模板参数
 */
interface FacialAnimationTemplateParameter {
  id: string;
  name: string;
  type: 'number' | 'boolean' | 'enum' | 'string';
  defaultValue: any;
  min?: number;
  max?: number;
  step?: number;
  options?: { value: any; label: string }[];
}

/**
 * 面部动画模板
 */
interface FacialAnimationTemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  tags?: string[];
  parameters: FacialAnimationTemplateParameter[];
  thumbnail?: string;
  previewGif?: string;
  author?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * 面部动画模板管理器属性
 */
interface FacialAnimationTemplateManagerProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 应用模板回调 */
  onTemplateApply?: (template: FacialAnimationTemplate, parameters: any) => void;
  /** 导入模板回调 */
  onTemplateImport?: (templates: FacialAnimationTemplate[]) => void;
  /** 导出模板回调 */
  onTemplateExport?: (templates: FacialAnimationTemplate[]) => void;
}

/**
 * 面部动画模板管理器
 */
export const FacialAnimationTemplateManager: React.FC<FacialAnimationTemplateManagerProps> = ({
  entityId,
  editable = true,
  onTemplateApply,
  onTemplateImport,
  onTemplateExport
}) => {
  const { t } = useTranslation();
  
  // 状态
  const [templates, setTemplates] = useState<FacialAnimationTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<FacialAnimationTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<FacialAnimationTemplate | null>(null);
  const [searchText, setSearchText] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [isPreviewVisible, setIsPreviewVisible] = useState<boolean>(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState<boolean>(false);
  const [isImportModalVisible, setIsImportModalVisible] = useState<boolean>(false);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [templateParameters, setTemplateParameters] = useState<any>({});
  
  // 表单引用
  const [form] = Form.useForm();
  
  // 加载模板
  useEffect(() => {
    loadTemplates();
  }, []);
  
  // 过滤模板
  useEffect(() => {
    filterTemplates();
  }, [templates, categoryFilter, searchText]);
  
  // 加载模板
  const loadTemplates = async () => {
    try {
      // 这里应该从引擎中加载模板
      // 示例数据，实际实现需要与引擎集成
      const mockTemplates: FacialAnimationTemplate[] = [
        {
          id: 'idle',
          name: '待机',
          description: '待机动画模板',
          category: '面部',
          tags: ['待机', '面部', '眨眼', '呼吸'],
          parameters: [
            {
              id: 'duration',
              name: '持续时间',
              type: 'number',
              defaultValue: 15.0,
              min: 5,
              max: 60,
              step: 0.1
            },
            {
              id: 'expression',
              name: '表情',
              type: 'enum',
              defaultValue: FacialExpressionType.NEUTRAL,
              options: [
                { value: FacialExpressionType.NEUTRAL, label: '中性' },
                { value: FacialExpressionType.HAPPY, label: '开心' },
                { value: FacialExpressionType.SAD, label: '悲伤' }
              ]
            },
            {
              id: 'expressionIntensity',
              name: '表情强度',
              type: 'number',
              defaultValue: 0.3,
              min: 0,
              max: 1,
              step: 0.01
            },
            {
              id: 'loop',
              name: '循环',
              type: 'boolean',
              defaultValue: true
            }
          ],
          author: '系统',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'talk',
          name: '说话',
          description: '说话动画模板',
          category: '口型',
          tags: ['口型', '说话', '对话'],
          parameters: [
            {
              id: 'duration',
              name: '持续时间',
              type: 'number',
              defaultValue: 5.0,
              min: 1,
              max: 30,
              step: 0.1
            },
            {
              id: 'visemeIntensity',
              name: '口型强度',
              type: 'number',
              defaultValue: 1.0,
              min: 0,
              max: 1,
              step: 0.01
            },
            {
              id: 'speed',
              name: '速度',
              type: 'number',
              defaultValue: 1.0,
              min: 0.5,
              max: 2,
              step: 0.1
            },
            {
              id: 'randomness',
              name: '随机性',
              type: 'number',
              defaultValue: 0.3,
              min: 0,
              max: 1,
              step: 0.01
            },
            {
              id: 'loop',
              name: '循环',
              type: 'boolean',
              defaultValue: true
            }
          ],
          author: '系统',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'emotional_talk',
          name: '情感对话',
          description: '带情感的说话动画模板',
          category: '组合',
          tags: ['口型', '说话', '情感', '组合'],
          parameters: [
            {
              id: 'expression',
              name: '表情',
              type: 'enum',
              defaultValue: FacialExpressionType.HAPPY,
              options: [
                { value: FacialExpressionType.HAPPY, label: '开心' },
                { value: FacialExpressionType.SAD, label: '悲伤' },
                { value: FacialExpressionType.ANGRY, label: '生气' },
                { value: FacialExpressionType.SURPRISED, label: '惊讶' }
              ]
            },
            {
              id: 'expressionIntensity',
              name: '表情强度',
              type: 'number',
              defaultValue: 0.7,
              min: 0,
              max: 1,
              step: 0.01
            },
            {
              id: 'duration',
              name: '持续时间',
              type: 'number',
              defaultValue: 5.0,
              min: 1,
              max: 30,
              step: 0.1
            },
            {
              id: 'visemeIntensity',
              name: '口型强度',
              type: 'number',
              defaultValue: 1.0,
              min: 0,
              max: 1,
              step: 0.01
            },
            {
              id: 'speed',
              name: '速度',
              type: 'number',
              defaultValue: 1.0,
              min: 0.5,
              max: 2,
              step: 0.1
            },
            {
              id: 'randomness',
              name: '随机性',
              type: 'number',
              defaultValue: 0.3,
              min: 0,
              max: 1,
              step: 0.01
            },
            {
              id: 'loop',
              name: '循环',
              type: 'boolean',
              defaultValue: true
            }
          ],
          author: '系统',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      setTemplates(mockTemplates);
    } catch (error) {
      console.error('加载模板失败:', error);
      message.error(t('editor.animation.loadTemplatesFailed'));
    }
  };
  
  // 过滤模板
  const filterTemplates = () => {
    let filtered = [...templates];
    
    // 按类别过滤
    if (categoryFilter) {
      filtered = filtered.filter(template => template.category === categoryFilter);
    }
    
    // 按搜索文本过滤
    if (searchText) {
      const lowerSearchText = searchText.toLowerCase();
      filtered = filtered.filter(template => 
        template.name.toLowerCase().includes(lowerSearchText) || 
        template.description?.toLowerCase().includes(lowerSearchText) ||
        template.tags?.some(tag => tag.toLowerCase().includes(lowerSearchText))
      );
    }
    
    setFilteredTemplates(filtered);
  };
  
  // 获取所有类别
  const getCategories = () => {
    const categories = new Set<string>();
    templates.forEach(template => categories.add(template.category));
    return Array.from(categories);
  };
  
  // 应用模板
  const applyTemplate = (template: FacialAnimationTemplate) => {
    if (onTemplateApply) {
      onTemplateApply(template, templateParameters);
    }
    
    message.success(t('editor.animation.templateApplied', { name: template.name }));
  };
  
  // 预览模板
  const previewTemplate = (template: FacialAnimationTemplate) => {
    setSelectedTemplate(template);
    
    // 初始化参数
    const params: any = {};
    template.parameters.forEach(param => {
      params[param.id] = param.defaultValue;
    });
    setTemplateParameters(params);
    
    setIsPreviewVisible(true);
    setIsPlaying(true);
  };
  
  // 编辑模板
  const editTemplate = (template: FacialAnimationTemplate) => {
    setSelectedTemplate(template);
    form.setFieldsValue({
      name: template.name,
      description: template.description,
      category: template.category,
      tags: template.tags?.join(', ')
    });
    setIsEditModalVisible(true);
  };
  
  // 删除模板
  const deleteTemplate = async (template: FacialAnimationTemplate) => {
    try {
      // 这里应该从引擎中删除模板
      // 示例代码，实际实现需要与引擎集成
      setTemplates(templates.filter(t => t.id !== template.id));
      message.success(t('editor.animation.templateDeleted', { name: template.name }));
    } catch (error) {
      console.error('删除模板失败:', error);
      message.error(t('editor.animation.deleteTemplateFailed'));
    }
  };
  
  // 创建新模板
  const createTemplate = () => {
    setSelectedTemplate(null);
    form.resetFields();
    setIsEditModalVisible(true);
  };
  
  // 保存模板
  const saveTemplate = async (values: any) => {
    try {
      // 解析标签
      const tags = values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : [];
      
      // 创建模板对象
      const template: FacialAnimationTemplate = {
        id: selectedTemplate?.id || `template_${Date.now()}`,
        name: values.name,
        description: values.description,
        category: values.category,
        tags,
        parameters: selectedTemplate?.parameters || [],
        author: '用户',
        createdAt: selectedTemplate?.createdAt || new Date(),
        updatedAt: new Date()
      };
      
      // 更新或添加模板
      if (selectedTemplate) {
        // 更新模板
        setTemplates(templates.map(t => t.id === template.id ? template : t));
        message.success(t('editor.animation.templateUpdated', { name: template.name }));
      } else {
        // 添加模板
        setTemplates([...templates, template]);
        message.success(t('editor.animation.templateCreated', { name: template.name }));
      }
      
      // 关闭编辑模态框
      setIsEditModalVisible(false);
    } catch (error) {
      console.error('保存模板失败:', error);
      message.error(t('editor.animation.saveTemplateFailed'));
    }
  };
  
  // 导入模板
  const importTemplates = async (importedTemplates: FacialAnimationTemplate[]) => {
    try {
      // 合并模板
      const mergedTemplates = [...templates];
      
      for (const template of importedTemplates) {
        const existingIndex = mergedTemplates.findIndex(t => t.id === template.id);
        
        if (existingIndex >= 0) {
          // 更新现有模板
          mergedTemplates[existingIndex] = template;
        } else {
          // 添加新模板
          mergedTemplates.push(template);
        }
      }
      
      // 更新模板列表
      setTemplates(mergedTemplates);
      
      // 回调
      if (onTemplateImport) {
        onTemplateImport(importedTemplates);
      }
      
      message.success(t('editor.animation.templatesImported', { count: importedTemplates.length }));
      
      // 关闭导入模态框
      setIsImportModalVisible(false);
    } catch (error) {
      console.error('导入模板失败:', error);
      message.error(t('editor.animation.importTemplatesFailed'));
    }
  };
  
  // 导出模板
  const exportTemplates = async () => {
    try {
      // 导出所有模板
      const templatesToExport = filteredTemplates.length > 0 ? filteredTemplates : templates;
      
      // 创建JSON数据
      const jsonData = JSON.stringify(templatesToExport, null, 2);
      
      // 创建下载链接
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `facial_animation_templates_${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      // 回调
      if (onTemplateExport) {
        onTemplateExport(templatesToExport);
      }
      
      message.success(t('editor.animation.templatesExported', { count: templatesToExport.length }));
    } catch (error) {
      console.error('导出模板失败:', error);
      message.error(t('editor.animation.exportTemplatesFailed'));
    }
  };
  
  // 更新参数值
  const updateParameterValue = (paramId: string, value: any) => {
    setTemplateParameters({
      ...templateParameters,
      [paramId]: value
    });
  };
  
  // 渲染参数控件
  const renderParameterControl = (param: FacialAnimationTemplateParameter) => {
    const value = templateParameters[param.id] !== undefined ? templateParameters[param.id] : param.defaultValue;
    
    switch (param.type) {
      case 'number':
        return (
          <div className="parameter-control">
            <div className="parameter-label">{param.name}: {value}</div>
            <Slider
              min={param.min || 0}
              max={param.max || 1}
              step={param.step || 0.01}
              value={value}
              onChange={(val) => updateParameterValue(param.id, val)}
            />
          </div>
        );
        
      case 'boolean':
        return (
          <div className="parameter-control">
            <div className="parameter-label">{param.name}</div>
            <Switch
              checked={value}
              onChange={(val) => updateParameterValue(param.id, val)}
            />
          </div>
        );
        
      case 'enum':
        return (
          <div className="parameter-control">
            <div className="parameter-label">{param.name}</div>
            <Select
              value={value}
              onChange={(val) => updateParameterValue(param.id, val)}
              style={{ width: '100%' }}
            >
              {param.options?.map(option => (
                <Option key={option.value} value={option.value}>{option.label}</Option>
              ))}
            </Select>
          </div>
        );
        
      case 'string':
        return (
          <div className="parameter-control">
            <div className="parameter-label">{param.name}</div>
            <Input
              value={value}
              onChange={(e) => updateParameterValue(param.id, e.target.value)}
            />
          </div>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className="facial-animation-template-manager">
      <div className="template-manager-header">
        <Input.Search
          placeholder={t('editor.animation.searchTemplates')}
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          style={{ width: 200 }}
        />
        
        <Select
          placeholder={t('editor.animation.filterByCategory')}
          value={categoryFilter}
          onChange={setCategoryFilter}
          style={{ width: 150 }}
          allowClear
        >
          {getCategories().map(category => (
            <Option key={category} value={category}>{category}</Option>
          ))}
        </Select>
        
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={createTemplate}
            disabled={!editable}
          >
            {t('editor.animation.createTemplate')}
          </Button>
          
          <Button
            icon={<ImportOutlined />}
            onClick={() => setIsImportModalVisible(true)}
            disabled={!editable}
          >
            {t('editor.animation.importTemplates')}
          </Button>
          
          <Button
            icon={<ExportOutlined />}
            onClick={exportTemplates}
          >
            {t('editor.animation.exportTemplates')}
          </Button>
        </Space>
      </div>
      
      <div className="template-list">
        {filteredTemplates.length === 0 ? (
          <Empty description={t('editor.animation.noTemplates')} />
        ) : (
          <List
            grid={{ gutter: 16, column: 3 }}
            dataSource={filteredTemplates}
            renderItem={template => (
              <List.Item>
                <Card
                  hoverable
                  cover={template.thumbnail && <img alt={template.name} src={template.thumbnail} />}
                  actions={[
                    <Tooltip title={t('editor.animation.previewTemplate')}>
                      <Button type="text" icon={<EyeOutlined />} onClick={() => previewTemplate(template)} />
                    </Tooltip>,
                    editable && (
                      <Tooltip title={t('editor.animation.editTemplate')}>
                        <Button type="text" icon={<EditOutlined />} onClick={() => editTemplate(template)} />
                      </Tooltip>
                    ),
                    editable && (
                      <Popconfirm
                        title={t('editor.animation.confirmDeleteTemplate')}
                        onConfirm={() => deleteTemplate(template)}
                        okText={t('common.yes')}
                        cancelText={t('common.no')}
                      >
                        <Tooltip title={t('editor.animation.deleteTemplate')}>
                          <Button type="text" icon={<DeleteOutlined />} />
                        </Tooltip>
                      </Popconfirm>
                    )
                  ]}
                >
                  <Card.Meta
                    title={template.name}
                    description={
                      <>
                        <div>{template.description}</div>
                        <div className="template-tags">
                          {template.tags?.map(tag => (
                            <Tag key={tag}>{tag}</Tag>
                          ))}
                        </div>
                      </>
                    }
                  />
                </Card>
              </List.Item>
            )}
          />
        )}
      </div>
      
      {/* 预览模态框 */}
      <Modal
        title={t('editor.animation.previewTemplate', { name: selectedTemplate?.name })}
        open={isPreviewVisible}
        onCancel={() => setIsPreviewVisible(false)}
        footer={[
          <Button
            key="apply"
            type="primary"
            onClick={() => {
              if (selectedTemplate) {
                applyTemplate(selectedTemplate);
              }
              setIsPreviewVisible(false);
            }}
          >
            {t('editor.animation.applyTemplate')}
          </Button>,
          <Button
            key="cancel"
            onClick={() => setIsPreviewVisible(false)}
          >
            {t('common.cancel')}
          </Button>
        ]}
        width={800}
      >
        <div className="template-preview">
          <div className="preview-container">
            <FacialAnimationPreview
              entityId={entityId}
              currentTime={currentTime}
              isPlaying={isPlaying}
            />
            
            <div className="preview-controls">
              <Button
                type="primary"
                shape="circle"
                icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={() => setIsPlaying(!isPlaying)}
              />
            </div>
          </div>
          
          <div className="template-parameters">
            <h3>{t('editor.animation.templateParameters')}</h3>
            
            <div className="parameters-list">
              {selectedTemplate?.parameters.map(param => (
                <div key={param.id} className="parameter-item">
                  {renderParameterControl(param)}
                </div>
              ))}
            </div>
          </div>
        </div>
      </Modal>
      
      {/* 编辑模态框 */}
      <Modal
        title={selectedTemplate ? t('editor.animation.editTemplate', { name: selectedTemplate.name }) : t('editor.animation.createTemplate')}
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={saveTemplate}
        >
          <Form.Item
            name="name"
            label={t('editor.animation.templateName')}
            rules={[{ required: true, message: t('editor.animation.templateNameRequired') }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="description"
            label={t('editor.animation.templateDescription')}
          >
            <Input.TextArea rows={2} />
          </Form.Item>
          
          <Form.Item
            name="category"
            label={t('editor.animation.category')}
            rules={[{ required: true, message: t('editor.animation.categoryRequired') }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="tags"
            label={t('editor.animation.tags')}
            help={t('editor.animation.tagsHelp')}
          >
            <Input />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {t('common.save')}
              </Button>
              <Button onClick={() => setIsEditModalVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 导入模态框 */}
      <Modal
        title={t('editor.animation.importTemplates')}
        open={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          accept=".json"
          beforeUpload={(file) => {
            const reader = new FileReader();
            reader.onload = (e) => {
              try {
                const importedTemplates = JSON.parse(e.target?.result as string);
                importTemplates(importedTemplates);
              } catch (error) {
                console.error('解析导入文件失败:', error);
                message.error(t('editor.animation.parseImportFileFailed'));
              }
            };
            reader.readAsText(file);
            return false;
          }}
          showUploadList={false}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">{t('editor.animation.clickOrDragToImport')}</p>
          <p className="ant-upload-hint">{t('editor.animation.importTemplateHint')}</p>
        </Upload.Dragger>
      </Modal>
    </div>
  );
};
