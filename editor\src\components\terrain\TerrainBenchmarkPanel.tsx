/**
 * 地形基准测试面板
 * 用于运行和显示地形性能基准测试
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Select, Slider, Switch, Space, Tooltip, Divider, Typography, Row, Col, Table, Progress, Tag, Tabs, Radio, Spin, message } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  LineChartOutlined,
  BarChartOutlined,
  DeleteOutlined,
  SaveOutlined,
  ExportOutlined,
  ImportOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
// 移除引擎直接导入
import { EngineService } from '../../services/EngineService';
import { SceneService } from '../../services/SceneService';
import './TerrainBenchmarkPanel.less';

const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Group: RadioGroup, Button: RadioButton } = Radio;

/**
 * 地形基准测试面板属性
 */
interface TerrainBenchmarkPanelProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
}

/**
 * 地形基准测试面板组件
 */
const TerrainBenchmarkPanel: React.FC<TerrainBenchmarkPanelProps> = ({
  entityId,
  editable = true
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 从Redux获取地形数据
  const terrainData = useSelector((state: RootState) => 
    entityId ? state.entities.byId[entityId]?.components?.TerrainComponent : null
  );
  
  // 基准测试器
  const benchmarkRef = useRef<TerrainPerformanceBenchmark | null>(null);
  
  // 状态
  const [activeTab, setActiveTab] = useState<string>('run');
  const [isTesting, setIsTesting] = useState<boolean>(false);
  const [testProgress, setTestProgress] = useState<number>(0);
  const [testResults, setTestResults] = useState<BenchmarkResult[]>([]);
  const [selectedResult, setSelectedResult] = useState<BenchmarkResult | null>(null);
  const [showSettings, setShowSettings] = useState<boolean>(false);
  
  // 配置
  const [testName, setTestName] = useState<string>('基准测试');
  const [testDuration, setTestDuration] = useState<number>(10000);
  const [warmupDuration, setWarmupDuration] = useState<number>(2000);
  const [sampleInterval, setSampleInterval] = useState<number>(100);
  const [cameraPathType, setCameraPathType] = useState<CameraPathType>(CameraPathType.CIRCULAR);
  const [testLOD, setTestLOD] = useState<boolean>(true);
  const [testFrustumCulling, setTestFrustumCulling] = useState<boolean>(true);
  const [testTextureStreaming, setTestTextureStreaming] = useState<boolean>(true);
  const [testVirtualTexturing, setTestVirtualTexturing] = useState<boolean>(true);
  const [testGeometryCompression, setTestGeometryCompression] = useState<boolean>(true);
  const [testPhysicsLOD, setTestPhysicsLOD] = useState<boolean>(true);
  
  // 初始化基准测试器
  useEffect(() => {
    // 获取基准测试器实例
    benchmarkRef.current = TerrainPerformanceBenchmark.getInstance();
    
    // 设置渲染器
    const engine = EngineService.getInstance().getEngine();
    if (engine) {
      const renderer = engine.getRenderer();
      if (renderer) {
        benchmarkRef.current.setRenderer(renderer);
      }
      
      // 设置相机
      const camera = engine.getActiveCamera();
      if (camera) {
        benchmarkRef.current.setCamera(camera);
      }
      
      // 设置场景
      const scene = SceneService.getInstance().getActiveScene();
      if (scene) {
        benchmarkRef.current.setScene(scene);
      }
    }
    
    // 注册事件监听器
    registerEventListeners();
    
    // 加载测试结果
    loadTestResults();
    
    // 清理函数
    return () => {
      unregisterEventListeners();
    };
  }, []);
  
  // 注册事件监听器
  const registerEventListeners = () => {
    if (!benchmarkRef.current) return;
    
    // 基准测试开始事件
    benchmarkRef.current.on(BenchmarkEventType.BENCHMARK_STARTED, (testName: string, config: BenchmarkConfig) => {
      setIsTesting(true);
      setTestProgress(0);
    });
    
    // 基准测试进度事件
    benchmarkRef.current.on(BenchmarkEventType.BENCHMARK_PROGRESS, (progress: number) => {
      setTestProgress(progress * 100);
    });
    
    // 基准测试完成事件
    benchmarkRef.current.on(BenchmarkEventType.BENCHMARK_COMPLETED, (result: BenchmarkResult) => {
      setIsTesting(false);
      setTestProgress(100);
      
      // 添加到测试结果
      setTestResults(prev => [...prev, result]);
      setSelectedResult(result);
      
      // 保存测试结果
      saveTestResults([...testResults, result]);
      
      message.success(t('terrain.benchmark.testCompleted'));
    });
    
    // 基准测试错误事件
    benchmarkRef.current.on(BenchmarkEventType.BENCHMARK_ERROR, (error: Error) => {
      setIsTesting(false);
      setTestProgress(0);
      message.error(t('terrain.benchmark.testError', { error: error.message }));
    });
  };
  
  // 取消注册事件监听器
  const unregisterEventListeners = () => {
    if (!benchmarkRef.current) return;
    
    benchmarkRef.current.off(BenchmarkEventType.BENCHMARK_STARTED, () => {});
    benchmarkRef.current.off(BenchmarkEventType.BENCHMARK_PROGRESS, () => {});
    benchmarkRef.current.off(BenchmarkEventType.BENCHMARK_COMPLETED, () => {});
    benchmarkRef.current.off(BenchmarkEventType.BENCHMARK_ERROR, () => {});
  };
  
  // 加载测试结果
  const loadTestResults = () => {
    try {
      // 从本地存储加载测试结果
      const savedResults = localStorage.getItem('terrainBenchmarkResults');
      if (savedResults) {
        const parsedResults = JSON.parse(savedResults) as BenchmarkResult[];
        setTestResults(parsedResults);
      }
    } catch (error) {
      console.error('加载测试结果失败:', error);
    }
  };
  
  // 保存测试结果
  const saveTestResults = (results: BenchmarkResult[]) => {
    try {
      // 保存测试结果到本地存储
      localStorage.setItem('terrainBenchmarkResults', JSON.stringify(results));
    } catch (error) {
      console.error('保存测试结果失败:', error);
    }
  };
  
  // 运行基准测试
  const runBenchmark = async () => {
    if (!benchmarkRef.current || isTesting) return;
    
    try {
      // 创建配置
      const config: BenchmarkConfig = {
        testDuration,
        warmupDuration,
        sampleInterval,
        cameraPathType,
        testLOD,
        testFrustumCulling,
        testTextureStreaming,
        testVirtualTexturing,
        testGeometryCompression,
        testPhysicsLOD
      };
      
      // 运行基准测试
      await benchmarkRef.current.runBenchmark(testName, config);
    } catch (error) {
      message.error(t('terrain.benchmark.runError', { error: (error as Error).message }));
    }
  };
  
  // 清除测试结果
  const clearTestResults = () => {
    setTestResults([]);
    setSelectedResult(null);
    saveTestResults([]);
    
    if (benchmarkRef.current) {
      benchmarkRef.current.clearAllTestResults();
    }
    
    message.success(t('terrain.benchmark.resultsCleared'));
  };
  
  // 导出测试结果
  const exportTestResults = () => {
    try {
      // 创建JSON数据
      const data = JSON.stringify(testResults, null, 2);
      
      // 创建Blob
      const blob = new Blob([data], { type: 'application/json' });
      
      // 创建URL
      const url = URL.createObjectURL(blob);
      
      // 创建链接
      const link = document.createElement('a');
      link.href = url;
      link.download = 'terrain_benchmark_results.json';
      
      // 点击链接
      document.body.appendChild(link);
      link.click();
      
      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      message.success(t('terrain.benchmark.exportSuccess'));
    } catch (error) {
      message.error(t('terrain.benchmark.exportError', { error: (error as Error).message }));
    }
  };
  
  // 导入测试结果
  const importTestResults = (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = event.target.files?.[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = e.target?.result as string;
          const parsedResults = JSON.parse(data) as BenchmarkResult[];
          
          setTestResults(parsedResults);
          saveTestResults(parsedResults);
          
          message.success(t('terrain.benchmark.importSuccess'));
        } catch (error) {
          message.error(t('terrain.benchmark.importError', { error: (error as Error).message }));
        }
      };
      
      reader.readAsText(file);
    } catch (error) {
      message.error(t('terrain.benchmark.importError', { error: (error as Error).message }));
    }
  };
  
  // 渲染运行面板
  const renderRunPanel = () => {
    return (
      <div className="benchmark-run-panel">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card title={t('terrain.benchmark.testConfiguration')}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.testName')}</Text>
                    <input
                      type="text"
                      value={testName}
                      onChange={(e) => setTestName(e.target.value)}
                      disabled={isTesting}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.cameraPath')}</Text>
                    <Select
                      value={cameraPathType}
                      onChange={(value) => setCameraPathType(value)}
                      disabled={isTesting}
                      style={{ width: '100%' }}
                    >
                      <Option value={CameraPathType.STATIC}>{t('terrain.benchmark.cameraPathStatic')}</Option>
                      <Option value={CameraPathType.CIRCULAR}>{t('terrain.benchmark.cameraPathCircular')}</Option>
                      <Option value={CameraPathType.FLYOVER}>{t('terrain.benchmark.cameraPathFlyover')}</Option>
                      <Option value={CameraPathType.RANDOM}>{t('terrain.benchmark.cameraPathRandom')}</Option>
                    </Select>
                  </div>
                </Col>
              </Row>
              
              <Divider />
              
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.testDuration')}</Text>
                    <Slider
                      min={5000}
                      max={30000}
                      step={1000}
                      value={testDuration}
                      onChange={(value) => setTestDuration(value)}
                      disabled={isTesting}
                    />
                    <Text type="secondary">{testDuration / 1000} {t('common.seconds')}</Text>
                  </div>
                </Col>
                <Col span={8}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.warmupDuration')}</Text>
                    <Slider
                      min={0}
                      max={5000}
                      step={500}
                      value={warmupDuration}
                      onChange={(value) => setWarmupDuration(value)}
                      disabled={isTesting}
                    />
                    <Text type="secondary">{warmupDuration / 1000} {t('common.seconds')}</Text>
                  </div>
                </Col>
                <Col span={8}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.sampleInterval')}</Text>
                    <Slider
                      min={50}
                      max={500}
                      step={50}
                      value={sampleInterval}
                      onChange={(value) => setSampleInterval(value)}
                      disabled={isTesting}
                    />
                    <Text type="secondary">{sampleInterval} ms</Text>
                  </div>
                </Col>
              </Row>
              
              <Divider />
              
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.testLOD')}</Text>
                    <Switch
                      checked={testLOD}
                      onChange={(checked) => setTestLOD(checked)}
                      disabled={isTesting}
                    />
                  </div>
                </Col>
                <Col span={8}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.testFrustumCulling')}</Text>
                    <Switch
                      checked={testFrustumCulling}
                      onChange={(checked) => setTestFrustumCulling(checked)}
                      disabled={isTesting}
                    />
                  </div>
                </Col>
                <Col span={8}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.testTextureStreaming')}</Text>
                    <Switch
                      checked={testTextureStreaming}
                      onChange={(checked) => setTestTextureStreaming(checked)}
                      disabled={isTesting}
                    />
                  </div>
                </Col>
              </Row>
              
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.testVirtualTexturing')}</Text>
                    <Switch
                      checked={testVirtualTexturing}
                      onChange={(checked) => setTestVirtualTexturing(checked)}
                      disabled={isTesting}
                    />
                  </div>
                </Col>
                <Col span={8}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.testGeometryCompression')}</Text>
                    <Switch
                      checked={testGeometryCompression}
                      onChange={(checked) => setTestGeometryCompression(checked)}
                      disabled={isTesting}
                    />
                  </div>
                </Col>
                <Col span={8}>
                  <div className="config-item">
                    <Text>{t('terrain.benchmark.testPhysicsLOD')}</Text>
                    <Switch
                      checked={testPhysicsLOD}
                      onChange={(checked) => setTestPhysicsLOD(checked)}
                      disabled={isTesting}
                    />
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
        
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={24}>
            <Card>
              <div className="benchmark-controls">
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  size="large"
                  onClick={runBenchmark}
                  disabled={isTesting}
                >
                  {t('terrain.benchmark.runTest')}
                </Button>
                
                {isTesting && (
                  <div className="benchmark-progress">
                    <Progress percent={Math.round(testProgress)} status="active" />
                    <Text>{t('terrain.benchmark.testing')}</Text>
                  </div>
                )}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };
  
  // 渲染结果面板
  const renderResultsPanel = () => {
    // 表格列
    const columns = [
      {
        title: t('terrain.benchmark.testName'),
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: t('terrain.benchmark.averageFPS'),
        dataIndex: 'averageFPS',
        key: 'averageFPS',
        render: (value: number) => value.toFixed(1),
        sorter: (a: BenchmarkResult, b: BenchmarkResult) => a.averageFPS - b.averageFPS,
      },
      {
        title: t('terrain.benchmark.minFPS'),
        dataIndex: 'minFPS',
        key: 'minFPS',
        render: (value: number) => value.toFixed(1),
      },
      {
        title: t('terrain.benchmark.maxFPS'),
        dataIndex: 'maxFPS',
        key: 'maxFPS',
        render: (value: number) => value.toFixed(1),
      },
      {
        title: t('terrain.benchmark.date'),
        dataIndex: 'timestamp',
        key: 'timestamp',
        render: (value: number) => new Date(value).toLocaleString(),
      },
    ];
    
    return (
      <div className="benchmark-results-panel">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card
              title={t('terrain.benchmark.testResults')}
              extra={
                <Space>
                  <Button
                    icon={<DeleteOutlined />}
                    onClick={clearTestResults}
                    disabled={testResults.length === 0}
                  >
                    {t('terrain.benchmark.clearResults')}
                  </Button>
                  <Button
                    icon={<ExportOutlined />}
                    onClick={exportTestResults}
                    disabled={testResults.length === 0}
                  >
                    {t('terrain.benchmark.exportResults')}
                  </Button>
                  <input
                    type="file"
                    id="import-results"
                    accept=".json"
                    style={{ display: 'none' }}
                    onChange={importTestResults}
                  />
                  <Button
                    icon={<ImportOutlined />}
                    onClick={() => document.getElementById('import-results')?.click()}
                  >
                    {t('terrain.benchmark.importResults')}
                  </Button>
                </Space>
              }
            >
              <Table
                dataSource={testResults}
                columns={columns}
                rowKey="timestamp"
                pagination={false}
                onRow={(record) => ({
                  onClick: () => setSelectedResult(record),
                })}
                rowClassName={(record) => record === selectedResult ? 'selected-row' : ''}
              />
            </Card>
          </Col>
        </Row>
        
        {selectedResult && (
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card title={t('terrain.benchmark.resultDetails')}>
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Statistic
                      title={t('terrain.benchmark.averageFPS')}
                      value={selectedResult.averageFPS}
                      precision={1}
                      suffix="FPS"
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title={t('terrain.benchmark.averageFrameTime')}
                      value={selectedResult.averageFrameTime}
                      precision={2}
                      suffix="ms"
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title={t('terrain.benchmark.fpsStdDev')}
                      value={selectedResult.fpsStdDev}
                      precision={2}
                    />
                  </Col>
                </Row>
                
                <Divider />
                
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Statistic
                      title={t('terrain.benchmark.averageRenderTime')}
                      value={selectedResult.averageRenderTime}
                      precision={2}
                      suffix="ms"
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title={t('terrain.benchmark.averagePhysicsTime')}
                      value={selectedResult.averagePhysicsTime}
                      precision={2}
                      suffix="ms"
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title={t('terrain.benchmark.averageMemoryUsage')}
                      value={selectedResult.averageMemoryUsage / (1024 * 1024)}
                      precision={1}
                      suffix="MB"
                    />
                  </Col>
                </Row>
                
                <Divider />
                
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Statistic
                      title={t('terrain.benchmark.averageVisibleTerrainChunks')}
                      value={selectedResult.averageVisibleTerrainChunks}
                      precision={0}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title={t('terrain.benchmark.averageTerrainTriangles')}
                      value={selectedResult.averageTerrainTriangles}
                      precision={0}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title={t('terrain.benchmark.averageTerrainVertices')}
                      value={selectedResult.averageTerrainVertices}
                      precision={0}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        )}
      </div>
    );
  };
  
  return (
    <div className="terrain-benchmark-panel">
      <Card
        title={
          <Space>
            <LineChartOutlined />
            <span>{t('terrain.benchmark.title')}</span>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('terrain.benchmark.run')} key="run">
            {renderRunPanel()}
          </TabPane>
          <TabPane tab={t('terrain.benchmark.results')} key="results">
            {renderResultsPanel()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default TerrainBenchmarkPanel;
