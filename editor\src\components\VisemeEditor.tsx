/**
 * 口型编辑器组件
 * 提供直观的口型编辑功能
 */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Select, Input, Switch, Tooltip, message, Space, Divider, Card, Tabs, Slider, Upload, Radio } from 'antd';
import { PlusOutlined, DeleteOutlined, SaveOutlined, UndoOutlined, RedoOutlined, UploadOutlined, PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
// 移除引擎直接导入
import './VisemeEditor.css';

const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 口型数据
 */
interface VisemeData {
  /** 口型类型 */
  type: VisemeType;
  /** 口型名称 */
  name: string;
  /** 描述 */
  description: string;
  /** 对应的音素 */
  phonemes: string[];
  /** 预览图 */
  previewImage?: string;
  /** 权重 */
  weight: number;
  /** 混合时间 */
  blendTime: number;
  /** 是否启用 */
  enabled: boolean;
  /** 自定义属性 */
  customProperties?: any;
}

/**
 * 口型序列项
 */
interface VisemeSequenceItem {
  /** 口型类型 */
  viseme: VisemeType;
  /** 开始时间 */
  startTime: number;
  /** 持续时间 */
  duration: number;
  /** 权重 */
  weight: number;
}

/**
 * 口型编辑器属性
 */
interface VisemeEditorProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 当前口型 */
  viseme?: VisemeType;
  /** 当前权重 */
  weight?: number;
  /** 组件宽度 */
  width?: number | string;
  /** 组件高度 */
  height?: number | string;
  /** 是否显示预览 */
  showPreview?: boolean;
  /** 是否显示音频波形 */
  showAudioWaveform?: boolean;
  /** 是否使用音频同步 */
  useAudioSync?: boolean;
  /** 音频文件 */
  audioFile?: string;
  /** 口型变更回调 */
  onVisemeChange?: (viseme: VisemeType, weight: number) => void;
  /** 口型序列变更回调 */
  onSequenceChange?: (sequence: VisemeSequenceItem[]) => void;
  /** 音频文件变更回调 */
  onAudioFileChange?: (file: string) => void;
  /** 保存回调 */
  onSave?: () => void;
}

/**
 * 口型编辑器组件
 */
export const VisemeEditor: React.FC<VisemeEditorProps> = ({
  entityId,
  editable = true,
  viseme = VisemeType.SILENT,
  weight = 1.0,
  width = '100%',
  height = '100%',
  showPreview = true,
  showAudioWaveform = true,
  useAudioSync = false,
  audioFile,
  onVisemeChange,
  onSequenceChange,
  onAudioFileChange,
  onSave
}) => {
  // 状态
  const [currentViseme, setCurrentViseme] = useState<VisemeType>(viseme);
  const [currentWeight, setCurrentWeight] = useState<number>(weight);
  const [visemes, setVisemes] = useState<VisemeData[]>([]);
  const [sequence, setSequence] = useState<VisemeSequenceItem[]>([]);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<string>('editor');
  const [selectedSequenceIndex, setSelectedSequenceIndex] = useState<number | null>(null);
  const [audioAnalysisResult, setAudioAnalysisResult] = useState<any>(null);
  const [isAnalyzingAudio, setIsAnalyzingAudio] = useState<boolean>(false);
  const [autoGenerateSequence, setAutoGenerateSequence] = useState<boolean>(false);
  const [language, setLanguage] = useState<string>('zh-CN');
  
  // 引用
  const audioRef = useRef<HTMLAudioElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  
  // Redux
  const dispatch = useDispatch();
  const visemeState = useSelector((state: RootState) => state.visemes);
  
  // 语言选项
  const languageOptions = [
    { value: 'zh-CN', label: '中文' },
    { value: 'en-US', label: '英语' },
    { value: 'ja-JP', label: '日语' },
    { value: 'ko-KR', label: '韩语' }
  ];
  
  // 模拟数据
  useEffect(() => {
    // 这里应该从Redux或API获取数据
    // 这里使用模拟数据
    const mockVisemes: VisemeData[] = [
      {
        type: VisemeType.SILENT,
        name: '静音',
        description: '闭嘴状态',
        phonemes: ['sil', 'sp'],
        previewImage: '/images/visemes/silent.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.AA,
        name: 'AA',
        description: '大开口元音',
        phonemes: ['aa', 'ah', 'ao'],
        previewImage: '/images/visemes/aa.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.CH,
        name: 'CH',
        description: '齿音',
        phonemes: ['ch', 'sh', 'zh'],
        previewImage: '/images/visemes/ch.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.DD,
        name: 'DD',
        description: '舌尖音',
        phonemes: ['d', 't', 'z', 'th'],
        previewImage: '/images/visemes/dd.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.EE,
        name: 'EE',
        description: '闭口元音',
        phonemes: ['ee', 'i', 'y'],
        previewImage: '/images/visemes/ee.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.FF,
        name: 'FF',
        description: '唇齿音',
        phonemes: ['f', 'v'],
        previewImage: '/images/visemes/ff.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.IH,
        name: 'IH',
        description: '半闭元音',
        phonemes: ['ih', 'ix'],
        previewImage: '/images/visemes/ih.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.KK,
        name: 'KK',
        description: '软腭音',
        phonemes: ['k', 'g', 'ng'],
        previewImage: '/images/visemes/kk.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.NN,
        name: 'NN',
        description: '鼻音',
        phonemes: ['n', 'm'],
        previewImage: '/images/visemes/nn.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.OH,
        name: 'OH',
        description: '圆唇元音',
        phonemes: ['oh', 'oy'],
        previewImage: '/images/visemes/oh.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.OU,
        name: 'OU',
        description: '圆唇后元音',
        phonemes: ['ow', 'uw', 'w'],
        previewImage: '/images/visemes/ou.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.PP,
        name: 'PP',
        description: '双唇音',
        phonemes: ['p', 'b', 'm'],
        previewImage: '/images/visemes/pp.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.RR,
        name: 'RR',
        description: '卷舌音',
        phonemes: ['r', 'er'],
        previewImage: '/images/visemes/rr.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.SS,
        name: 'SS',
        description: '摩擦音',
        phonemes: ['s', 'z'],
        previewImage: '/images/visemes/ss.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      },
      {
        type: VisemeType.TH,
        name: 'TH',
        description: '齿间音',
        phonemes: ['th', 'dh'],
        previewImage: '/images/visemes/th.png',
        weight: 1.0,
        blendTime: 0.1,
        enabled: true
      }
    ];
    
    setVisemes(mockVisemes);
    
    // 模拟序列数据
    const mockSequence: VisemeSequenceItem[] = [
      { viseme: VisemeType.SILENT, startTime: 0, duration: 0.5, weight: 1.0 },
      { viseme: VisemeType.PP, startTime: 0.5, duration: 0.2, weight: 1.0 },
      { viseme: VisemeType.AA, startTime: 0.7, duration: 0.3, weight: 1.0 },
      { viseme: VisemeType.NN, startTime: 1.0, duration: 0.2, weight: 1.0 },
      { viseme: VisemeType.DD, startTime: 1.2, duration: 0.3, weight: 1.0 },
      { viseme: VisemeType.AA, startTime: 1.5, duration: 0.5, weight: 1.0 },
      { viseme: VisemeType.SILENT, startTime: 2.0, duration: 0.5, weight: 1.0 }
    ];
    
    setSequence(mockSequence);
    setDuration(2.5);
  }, []);
  
  // 处理口型变更
  const handleVisemeChange = (newViseme: VisemeType) => {
    setCurrentViseme(newViseme);
    
    if (onVisemeChange) {
      onVisemeChange(newViseme, currentWeight);
    }
  };
  
  // 处理权重变更
  const handleWeightChange = (newWeight: number) => {
    setCurrentWeight(newWeight);
    
    if (onVisemeChange) {
      onVisemeChange(currentViseme, newWeight);
    }
  };
  
  // 处理序列项添加
  const handleAddSequenceItem = () => {
    // 找到最后一项的结束时间
    const lastItem = sequence[sequence.length - 1];
    const startTime = lastItem ? lastItem.startTime + lastItem.duration : 0;
    
    const newItem: VisemeSequenceItem = {
      viseme: VisemeType.SILENT,
      startTime,
      duration: 0.3,
      weight: 1.0
    };
    
    const newSequence = [...sequence, newItem];
    setSequence(newSequence);
    setSelectedSequenceIndex(newSequence.length - 1);
    
    if (onSequenceChange) {
      onSequenceChange(newSequence);
    }
  };
  
  // 处理序列项删除
  const handleDeleteSequenceItem = (index: number) => {
    const newSequence = [...sequence];
    newSequence.splice(index, 1);
    setSequence(newSequence);
    
    if (selectedSequenceIndex === index) {
      setSelectedSequenceIndex(null);
    } else if (selectedSequenceIndex !== null && selectedSequenceIndex > index) {
      setSelectedSequenceIndex(selectedSequenceIndex - 1);
    }
    
    if (onSequenceChange) {
      onSequenceChange(newSequence);
    }
  };
  
  // 处理序列项属性变更
  const handleSequenceItemChange = (index: number, property: keyof VisemeSequenceItem, value: any) => {
    const newSequence = [...sequence];
    newSequence[index] = { ...newSequence[index], [property]: value };
    
    // 如果修改了开始时间或持续时间，需要重新排序
    if (property === 'startTime' || property === 'duration') {
      newSequence.sort((a, b) => a.startTime - b.startTime);
      
      // 更新选中项索引
      if (selectedSequenceIndex !== null) {
        const selectedItem = sequence[selectedSequenceIndex];
        const newIndex = newSequence.findIndex(item => 
          item.viseme === selectedItem.viseme && 
          item.startTime === (property === 'startTime' && index === selectedSequenceIndex ? value : selectedItem.startTime) &&
          item.duration === (property === 'duration' && index === selectedSequenceIndex ? value : selectedItem.duration)
        );
        setSelectedSequenceIndex(newIndex);
      }
    }
    
    setSequence(newSequence);
    
    if (onSequenceChange) {
      onSequenceChange(newSequence);
    }
  };
  
  // 处理音频文件上传
  const handleAudioUpload = (file: any) => {
    if (onAudioFileChange) {
      onAudioFileChange(file);
    }
    
    // 模拟音频分析
    setIsAnalyzingAudio(true);
    
    // 模拟异步操作
    setTimeout(() => {
      setIsAnalyzingAudio(false);
      setAudioAnalysisResult({
        duration: 5.0,
        phonemes: [
          { phoneme: 'sil', startTime: 0, endTime: 0.5 },
          { phoneme: 'p', startTime: 0.5, endTime: 0.7 },
          { phoneme: 'aa', startTime: 0.7, endTime: 1.0 },
          { phoneme: 'n', startTime: 1.0, endTime: 1.2 },
          { phoneme: 'd', startTime: 1.2, endTime: 1.5 },
          { phoneme: 'aa', startTime: 1.5, endTime: 2.0 },
          { phoneme: 'sil', startTime: 2.0, endTime: 2.5 }
        ]
      });
      
      if (autoGenerateSequence) {
        generateSequenceFromAudio();
      }
      
      message.success('音频分析完成');
    }, 2000);
  };
  
  // 从音频生成序列
  const generateSequenceFromAudio = () => {
    if (!audioAnalysisResult) {
      message.error('请先上传并分析音频');
      return;
    }
    
    // 将音素映射到口型
    const newSequence: VisemeSequenceItem[] = [];
    
    for (const phonemeData of audioAnalysisResult.phonemes) {
      // 查找对应的口型
      const matchingViseme = visemes.find(v => v.phonemes.includes(phonemeData.phoneme));
      
      if (matchingViseme) {
        newSequence.push({
          viseme: matchingViseme.type,
          startTime: phonemeData.startTime,
          duration: phonemeData.endTime - phonemeData.startTime,
          weight: 1.0
        });
      }
    }
    
    setSequence(newSequence);
    setDuration(audioAnalysisResult.duration);
    
    if (onSequenceChange) {
      onSequenceChange(newSequence);
    }
    
    message.success('已从音频生成口型序列');
  };
  
  // 处理播放/暂停
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
    }
  };
  
  // 处理保存
  const handleSave = () => {
    if (onSave) {
      onSave();
    }
    message.success('保存成功');
  };
  
  return (
    <div className="viseme-editor" style={{ width, height }}>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="口型编辑器" key="editor">
          <div className="viseme-editor-content">
            <div className="viseme-controls">
              <div className="control-group">
                <label>口型:</label>
                <Select
                  value={currentViseme}
                  onChange={handleVisemeChange}
                  disabled={!editable}
                  style={{ width: 120 }}
                >
                  {visemes.map(v => (
                    <Option key={v.type} value={v.type}>{v.name}</Option>
                  ))}
                </Select>
              </div>
              
              <div className="control-group">
                <label>权重:</label>
                <Slider
                  value={currentWeight}
                  onChange={handleWeightChange}
                  min={0}
                  max={1}
                  step={0.01}
                  disabled={!editable}
                  style={{ width: 200 }}
                />
              </div>
              
              <Button
                icon={<SaveOutlined />}
                onClick={handleSave}
                disabled={!editable}
              >
                保存
              </Button>
            </div>
            
            {showPreview && (
              <div className="viseme-preview">
                <h3>预览</h3>
                <div className="preview-image-container">
                  {visemes.find(v => v.type === currentViseme)?.previewImage ? (
                    <img
                      src={visemes.find(v => v.type === currentViseme)?.previewImage}
                      alt={`${currentViseme} 预览`}
                      className="preview-image"
                    />
                  ) : (
                    <div className="preview-placeholder">无预览图</div>
                  )}
                </div>
                <div className="preview-description">
                  {visemes.find(v => v.type === currentViseme)?.description || ''}
                </div>
              </div>
            )}
          </div>
        </TabPane>
        
        <TabPane tab="口型序列" key="sequence">
          <div className="sequence-editor-content">
            <div className="sequence-controls">
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddSequenceItem}
                  disabled={!editable}
                >
                  添加口型
                </Button>
                
                <Button
                  icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={handlePlayPause}
                >
                  {isPlaying ? '暂停' : '播放'}
                </Button>
                
                <Upload
                  accept="audio/*"
                  showUploadList={false}
                  beforeUpload={(file) => {
                    handleAudioUpload(file);
                    return false;
                  }}
                >
                  <Button icon={<UploadOutlined />} loading={isAnalyzingAudio}>
                    上传音频
                  </Button>
                </Upload>
                
                <div className="control-group">
                  <Switch
                    checked={autoGenerateSequence}
                    onChange={setAutoGenerateSequence}
                    disabled={!editable}
                  />
                  <label>自动生成序列</label>
                </div>
                
                <div className="control-group">
                  <label>语言:</label>
                  <Select
                    value={language}
                    onChange={setLanguage}
                    disabled={!editable}
                    style={{ width: 100 }}
                  >
                    {languageOptions.map(option => (
                      <Option key={option.value} value={option.value}>{option.label}</Option>
                    ))}
                  </Select>
                </div>
                
                <Button
                  onClick={generateSequenceFromAudio}
                  disabled={!audioAnalysisResult || !editable}
                >
                  从音频生成
                </Button>
              </Space>
            </div>
            
            <div className="sequence-timeline" ref={timelineRef}>
              {/* 时间轴实现 */}
              <div className="timeline-header">
                <div className="timeline-scale">
                  {Array.from({ length: Math.ceil(duration) + 1 }).map((_, i) => (
                    <div key={i} className="timeline-marker">
                      <div className="marker-line"></div>
                      <div className="marker-label">{i}s</div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="timeline-content">
                {showAudioWaveform && (
                  <div className="audio-waveform">
                    <canvas ref={canvasRef} height={50} />
                  </div>
                )}
                
                <div className="viseme-sequence">
                  {sequence.map((item, index) => (
                    <div
                      key={index}
                      className={`sequence-item ${selectedSequenceIndex === index ? 'selected' : ''}`}
                      style={{
                        left: `${(item.startTime / duration) * 100}%`,
                        width: `${(item.duration / duration) * 100}%`,
                        opacity: item.weight
                      }}
                      onClick={() => setSelectedSequenceIndex(index)}
                    >
                      <div className="item-label">{visemes.find(v => v.type === item.viseme)?.name || item.viseme}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {selectedSequenceIndex !== null && (
              <div className="sequence-item-editor">
                <h3>编辑口型项</h3>
                <div className="item-properties">
                  <div className="property-group">
                    <label>口型:</label>
                    <Select
                      value={sequence[selectedSequenceIndex].viseme}
                      onChange={(value) => handleSequenceItemChange(selectedSequenceIndex, 'viseme', value)}
                      disabled={!editable}
                      style={{ width: 120 }}
                    >
                      {visemes.map(v => (
                        <Option key={v.type} value={v.type}>{v.name}</Option>
                      ))}
                    </Select>
                  </div>
                  
                  <div className="property-group">
                    <label>开始时间 (秒):</label>
                    <Input
                      type="number"
                      value={sequence[selectedSequenceIndex].startTime}
                      onChange={(e) => handleSequenceItemChange(selectedSequenceIndex, 'startTime', parseFloat(e.target.value))}
                      disabled={!editable}
                      min={0}
                      step={0.1}
                      style={{ width: 100 }}
                    />
                  </div>
                  
                  <div className="property-group">
                    <label>持续时间 (秒):</label>
                    <Input
                      type="number"
                      value={sequence[selectedSequenceIndex].duration}
                      onChange={(e) => handleSequenceItemChange(selectedSequenceIndex, 'duration', parseFloat(e.target.value))}
                      disabled={!editable}
                      min={0.1}
                      step={0.1}
                      style={{ width: 100 }}
                    />
                  </div>
                  
                  <div className="property-group">
                    <label>权重:</label>
                    <Slider
                      value={sequence[selectedSequenceIndex].weight}
                      onChange={(value) => handleSequenceItemChange(selectedSequenceIndex, 'weight', value)}
                      min={0}
                      max={1}
                      step={0.01}
                      disabled={!editable}
                      style={{ width: 200 }}
                    />
                  </div>
                  
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteSequenceItem(selectedSequenceIndex)}
                    disabled={!editable}
                  >
                    删除
                  </Button>
                </div>
              </div>
            )}
          </div>
        </TabPane>
      </Tabs>
      
      {audioFile && (
        <audio ref={audioRef} src={audioFile} style={{ display: 'none' }} />
      )}
    </div>
  );
};
