/**
 * 遮挡剔除面板组件
 * 用于配置和监控遮挡剔除系统
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Switch, Select, Button, Tooltip, Divider, Space, Tag, Alert, Collapse, Progress, Statistic } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  ReloadOutlined,
  SettingOutlined,
  EyeOutlined,
  LineChartOutlined,
  ThunderboltOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  BarChartOutlined
} from '@ant-design/icons';
// 移除引擎直接导入
// 移除引擎直接导入
import { EngineService } from '../../services/EngineService';
import { SceneService } from '../../services/SceneService';
import './OcclusionCullingPanel.less';

const { Panel } = Collapse;
const { Option } = Select;

/**
 * 遮挡剔除面板组件
 */
const OcclusionCullingPanel: React.FC = () => {
  const { t } = useTranslation();
  
  // 状态
  const [isEnabled, setIsEnabled] = useState(true);
  const [algorithm, setAlgorithm] = useState<OcclusionCullingAlgorithm>(OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER);
  const [useAdaptiveAlgorithm, setUseAdaptiveAlgorithm] = useState(true);
  const [useMultiLevelCulling, setUseMultiLevelCulling] = useState(true);
  const [usePredictiveCulling, setUsePredictiveCulling] = useState(true);
  const [useTemporalCoherence, setUseTemporalCoherence] = useState(true);
  const [useGPUAcceleration, setUseGPUAcceleration] = useState(true);
  const [useConservativeCulling, setUseConservativeCulling] = useState(false);
  const [useDebugVisualization, setUseDebugVisualization] = useState(false);
  const [collectStats, setCollectStats] = useState(true);
  const [useAutoOptimization, setUseAutoOptimization] = useState(true);
  const [stats, setStats] = useState<OcclusionCullingStats | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [cullingSystem, setCullingSystem] = useState<EnhancedOcclusionCullingSystem | null>(null);
  
  // 引用
  const timerRef = useRef<number | null>(null);
  
  // 初始化
  useEffect(() => {
    // 获取引擎
    const engine = EngineService.getInstance().getEngine();
    if (!engine) {
      return;
    }
    
    // 获取遮挡剔除系统
    let system = engine.getSystem('EnhancedOcclusionCullingSystem') as EnhancedOcclusionCullingSystem;
    
    // 如果系统不存在，则创建
    if (!system) {
      system = new EnhancedOcclusionCullingSystem({
        enabled: isEnabled,
        algorithm,
        useAdaptiveAlgorithm,
        useMultiLevelCulling,
        usePredictiveCulling,
        useTemporalCoherence,
        useGPUAcceleration,
        useConservativeCulling,
        useDebugVisualization,
        collectStats,
        useAutoOptimization
      });
      
      // 添加到引擎
      engine.addSystem(system);
    }
    
    // 设置系统
    setCullingSystem(system);
    
    // 更新状态
    setIsEnabled(system.isEnabled());
    setAlgorithm(system.getAlgorithm());
    setUseDebugVisualization(system.isDebugVisualizationEnabled());
    
    // 添加事件监听器
    system.addEventListener(EnhancedOcclusionCullingSystemEventType.ALGORITHM_CHANGED, handleAlgorithmChanged);
    system.addEventListener(EnhancedOcclusionCullingSystemEventType.STATS_UPDATED, handleStatsUpdated);
    
    // 设置定时器定期更新统计信息
    timerRef.current = window.setInterval(() => {
      if (system && collectStats) {
        const latestStats = system.getLatestStats();
        if (latestStats) {
          setStats(latestStats);
        }
      }
    }, 500);
    
    setIsInitialized(true);
    
    // 清理函数
    return () => {
      // 移除事件监听器
      if (system) {
        system.removeEventListener(EnhancedOcclusionCullingSystemEventType.ALGORITHM_CHANGED, handleAlgorithmChanged);
        system.removeEventListener(EnhancedOcclusionCullingSystemEventType.STATS_UPDATED, handleStatsUpdated);
      }
      
      // 清除定时器
      if (timerRef.current !== null) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);
  
  // 处理算法变更事件
  const handleAlgorithmChanged = (newAlgorithm: OcclusionCullingAlgorithm, oldAlgorithm: OcclusionCullingAlgorithm) => {
    setAlgorithm(newAlgorithm);
  };
  
  // 处理统计更新事件
  const handleStatsUpdated = (newStats: OcclusionCullingStats) => {
    setStats(newStats);
  };
  
  // 启用/禁用系统
  const handleEnableChange = (checked: boolean) => {
    if (cullingSystem) {
      cullingSystem.setEnabled(checked);
      setIsEnabled(checked);
    }
  };
  
  // 设置算法
  const handleAlgorithmChange = (value: OcclusionCullingAlgorithm) => {
    if (cullingSystem) {
      cullingSystem.setAlgorithm(value);
      setAlgorithm(value);
    }
  };
  
  // 启用/禁用自适应算法
  const handleAdaptiveAlgorithmChange = (checked: boolean) => {
    setUseAdaptiveAlgorithm(checked);
    if (cullingSystem) {
      // 假设有这个方法
      (cullingSystem as any).useAdaptiveAlgorithm = checked;
    }
  };
  
  // 启用/禁用多级剔除
  const handleMultiLevelCullingChange = (checked: boolean) => {
    setUseMultiLevelCulling(checked);
    if (cullingSystem) {
      // 假设有这个方法
      (cullingSystem as any).useMultiLevelCulling = checked;
    }
  };
  
  // 启用/禁用预测剔除
  const handlePredictiveCullingChange = (checked: boolean) => {
    setUsePredictiveCulling(checked);
    if (cullingSystem) {
      // 假设有这个方法
      (cullingSystem as any).usePredictiveCulling = checked;
    }
  };
  
  // 启用/禁用时间一致性
  const handleTemporalCoherenceChange = (checked: boolean) => {
    setUseTemporalCoherence(checked);
    if (cullingSystem) {
      // 假设有这个方法
      (cullingSystem as any).useTemporalCoherence = checked;
    }
  };
  
  // 启用/禁用GPU加速
  const handleGPUAccelerationChange = (checked: boolean) => {
    setUseGPUAcceleration(checked);
    if (cullingSystem) {
      // 假设有这个方法
      (cullingSystem as any).useGPUAcceleration = checked;
    }
  };
  
  // 启用/禁用保守剔除
  const handleConservativeCullingChange = (checked: boolean) => {
    setUseConservativeCulling(checked);
    if (cullingSystem) {
      // 假设有这个方法
      (cullingSystem as any).useConservativeCulling = checked;
    }
  };
  
  // 启用/禁用调试可视化
  const handleDebugVisualizationChange = (checked: boolean) => {
    if (cullingSystem) {
      cullingSystem.setDebugVisualizationEnabled(checked);
      setUseDebugVisualization(checked);
    }
  };
  
  // 启用/禁用统计收集
  const handleCollectStatsChange = (checked: boolean) => {
    setCollectStats(checked);
    if (cullingSystem) {
      // 假设有这个方法
      (cullingSystem as any).collectStats = checked;
    }
  };
  
  // 启用/禁用自动优化
  const handleAutoOptimizationChange = (checked: boolean) => {
    setUseAutoOptimization(checked);
    if (cullingSystem) {
      // 假设有这个方法
      (cullingSystem as any).useAutoOptimization = checked;
    }
  };
  
  // 获取算法名称
  const getAlgorithmName = (algo: OcclusionCullingAlgorithm): string => {
    switch (algo) {
      case OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER:
        return t('optimization.occlusionCulling.algorithmHZB');
      case OcclusionCullingAlgorithm.OCCLUSION_QUERY:
        return t('optimization.occlusionCulling.algorithmOQ');
      case OcclusionCullingAlgorithm.SOFTWARE_RASTERIZATION:
        return t('optimization.occlusionCulling.algorithmSR');
      case OcclusionCullingAlgorithm.HARDWARE_OCCLUSION_QUERY:
        return t('optimization.occlusionCulling.algorithmHOQ');
      default:
        return t('optimization.occlusionCulling.algorithmUnknown');
    }
  };
  
  // 如果未初始化，显示加载中
  if (!isInitialized) {
    return (
      <Card title={t('optimization.occlusionCulling.title')} className="occlusion-culling-panel">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">{t('common.loading')}</div>
        </div>
      </Card>
    );
  }
  
  return (
    <Card 
      title={
        <Space>
          <span>{t('optimization.occlusionCulling.title')}</span>
          <Switch 
            checked={isEnabled} 
            onChange={handleEnableChange} 
            size="small"
          />
        </Space>
      } 
      className="occlusion-culling-panel"
      extra={
        <Tooltip title={t('common.refresh')}>
          <Button 
            icon={<ReloadOutlined />} 
            size="small" 
            type="text"
            onClick={() => {
              if (cullingSystem) {
                setIsEnabled(cullingSystem.isEnabled());
                setAlgorithm(cullingSystem.getAlgorithm());
                setUseDebugVisualization(cullingSystem.isDebugVisualizationEnabled());
              }
            }}
          />
        </Tooltip>
      }
    >
      <Collapse defaultActiveKey={['1', '2']}>
        <Panel header={t('optimization.occlusionCulling.currentStatus')} key="1">
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic 
                title={t('optimization.occlusionCulling.culledObjects')} 
                value={stats?.culledObjects || 0} 
                suffix={`/ ${stats?.totalObjects || 0}`}
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title={t('optimization.occlusionCulling.cullingRate')} 
                value={stats ? (stats.cullingRate * 100).toFixed(1) : '0.0'} 
                suffix="%" 
                valueStyle={{ color: stats && stats.cullingRate > 0.5 ? '#3f8600' : '#cf1322' }}
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title={t('optimization.occlusionCulling.cullingTime')} 
                value={stats ? stats.cullingTime.toFixed(2) : '0.00'} 
                suffix="ms"
              />
            </Col>
            <Col span={24}>
              <div className="algorithm-info">
                <div className="algorithm-label">{t('optimization.occlusionCulling.currentAlgorithm')}</div>
                <div className="algorithm-value">
                  <Tag color="blue">{getAlgorithmName(algorithm)}</Tag>
                  {useAdaptiveAlgorithm && (
                    <Tag color="green">{t('optimization.occlusionCulling.adaptive')}</Tag>
                  )}
                </div>
              </div>
            </Col>
            <Col span={24}>
              <Progress 
                percent={stats ? (stats.cullingRate * 100) : 0} 
                status="active" 
                strokeColor={{
                  '0%': '#ff4d4f',
                  '25%': '#faad14',
                  '50%': '#52c41a',
                  '75%': '#1890ff',
                  '100%': '#722ed1'
                }}
              />
            </Col>
          </Row>
        </Panel>
        
        <Panel header={t('optimization.occlusionCulling.settings')} key="2">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.occlusionCulling.algorithm')}
                </div>
                <div className="setting-control">
                  <Select 
                    value={algorithm} 
                    onChange={handleAlgorithmChange}
                    disabled={!isEnabled || useAdaptiveAlgorithm}
                    style={{ width: '100%' }}
                  >
                    <Option value={OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER}>
                      {t('optimization.occlusionCulling.algorithmHZB')}
                    </Option>
                    <Option value={OcclusionCullingAlgorithm.OCCLUSION_QUERY}>
                      {t('optimization.occlusionCulling.algorithmOQ')}
                    </Option>
                    <Option value={OcclusionCullingAlgorithm.SOFTWARE_RASTERIZATION}>
                      {t('optimization.occlusionCulling.algorithmSR')}
                    </Option>
                    <Option value={OcclusionCullingAlgorithm.HARDWARE_OCCLUSION_QUERY}>
                      {t('optimization.occlusionCulling.algorithmHOQ')}
                    </Option>
                  </Select>
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.occlusionCulling.useAdaptiveAlgorithm')}
                  <Tooltip title={t('optimization.occlusionCulling.useAdaptiveAlgorithmTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useAdaptiveAlgorithm} 
                    onChange={handleAdaptiveAlgorithmChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.occlusionCulling.useMultiLevelCulling')}
                  <Tooltip title={t('optimization.occlusionCulling.useMultiLevelCullingTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useMultiLevelCulling} 
                    onChange={handleMultiLevelCullingChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.occlusionCulling.usePredictiveCulling')}
                  <Tooltip title={t('optimization.occlusionCulling.usePredictiveCullingTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={usePredictiveCulling} 
                    onChange={handlePredictiveCullingChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.occlusionCulling.useTemporalCoherence')}
                  <Tooltip title={t('optimization.occlusionCulling.useTemporalCoherenceTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useTemporalCoherence} 
                    onChange={handleTemporalCoherenceChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.occlusionCulling.useGPUAcceleration')}
                  <Tooltip title={t('optimization.occlusionCulling.useGPUAccelerationTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useGPUAcceleration} 
                    onChange={handleGPUAccelerationChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.occlusionCulling.useConservativeCulling')}
                  <Tooltip title={t('optimization.occlusionCulling.useConservativeCullingTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useConservativeCulling} 
                    onChange={handleConservativeCullingChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.occlusionCulling.useDebugVisualization')}
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useDebugVisualization} 
                    onChange={handleDebugVisualizationChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.occlusionCulling.collectStats')}
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={collectStats} 
                    onChange={handleCollectStatsChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.occlusionCulling.useAutoOptimization')}
                  <Tooltip title={t('optimization.occlusionCulling.useAutoOptimizationTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useAutoOptimization} 
                    onChange={handleAutoOptimizationChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </Panel>
      </Collapse>
    </Card>
  );
};

export default OcclusionCullingPanel;
