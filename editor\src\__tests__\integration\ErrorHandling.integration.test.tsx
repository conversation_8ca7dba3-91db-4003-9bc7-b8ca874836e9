/**
 * 错误处理和异常恢复集成测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '../../__tests__/utils/test-utils';
import { EditorPage } from '../../pages/EditorPage';
import { jest } from '@jest/globals';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { ErrorBoundary } from '../../components/common/ErrorBoundary';

// 模拟服务
jest.mock('../../services/EngineService', () => ({
  __esModule: true,
  default: {
    initialize: jest.fn().mockRejectedValue(new Error('引擎初始化失败')),
    on: jest.fn(),
    off: jest.fn(),
    getActiveScene: jest.fn(),
    getActiveCamera: jest.fn(),
    EngineEventType: {
      OBJECT_SELECTED: 'object-selected',
      OBJECT_DESELECTED: 'object-deselected',
    },
  },
  EngineEventType: {
    OBJECT_SELECTED: 'object-selected',
    OBJECT_DESELECTED: 'object-deselected',
  },
  TransformMode: {
    TRANSLATE: 'translate',
    ROTATE: 'rotate',
    SCALE: 'scale',
  },
  TransformSpace: {
    LOCAL: 'local',
    WORLD: 'world',
  },
}));

// 创建一个会抛出错误的组件
const ErrorComponent = () => {
  throw new Error('测试错误');
};

// 测试ErrorBoundary组件
describe('错误边界测试', () => {
  it('应该捕获并显示组件错误', () => {
    render(
      <ErrorBoundary fallback={<div>出错了！请刷新页面重试。</div>}>
        <ErrorComponent />
      </ErrorBoundary>
    );

    expect(screen.getByText('出错了！请刷新页面重试。')).toBeInTheDocument();
  });
});

// 测试网络错误处理
describe('网络错误处理测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该处理API请求错误', async () => {
    // 模拟ProjectService.getProject返回错误
    const mockGetProject = jest.fn().mockRejectedValue(new Error('网络请求失败'));

    // 替换模拟实现
    jest.mock('../../services/ProjectService', () => ({
      __esModule: true,
      default: {
        getProject: mockGetProject,
        getScenes: jest.fn().mockResolvedValue([]),
      },
    }), { virtual: true });

    render(
      <MemoryRouter initialEntries={['/editor/test-project/test-scene']}>
        <Routes>
          <Route path="/editor/:projectId/:sceneId" element={<EditorPage />} />
        </Routes>
      </MemoryRouter>
    );

    // 等待错误消息显示
    await waitFor(() => {
      expect(screen.getByText(/加载项目失败/)).toBeInTheDocument();
    });
  });

  it('应该处理引擎初始化错误', async () => {
    // 模拟ProjectService.getProject返回成功
    const mockGetProject = jest.fn().mockResolvedValue({
      id: 'test-project',
      name: '测试项目',
      description: '测试项目描述',
      scenes: [
        {
          id: 'test-scene',
          name: '测试场景',
        },
      ],
    });

    // 替换模拟实现
    jest.mock('../../services/ProjectService', () => ({
      __esModule: true,
      default: {
        getProject: mockGetProject,
        getScenes: jest.fn().mockResolvedValue([
          {
            id: 'test-scene',
            name: '测试场景',
          },
        ]),
      },
    }), { virtual: true });

    // 模拟EditorLayout组件，使其能够触发引擎初始化错误
    jest.mock('../../components/layout/EditorLayout', () => ({
      __esModule: true,
      EditorLayout: ({ projectId, sceneId }: any) => {
        const [error, setError] = React.useState<string | null>(null);

        React.useEffect(() => {
          // 模拟引擎初始化错误
          setError('引擎初始化失败');
        }, []);

        if (error) {
          return <div data-testid="engine-error">{error}</div>;
        }

        return (
          <div data-testid="mock-editor-layout">
            <div>Project ID: {projectId}</div>
            <div>Scene ID: {sceneId}</div>
          </div>
        );
      },
    }), { virtual: true });

    render(
      <MemoryRouter initialEntries={['/editor/test-project/test-scene']}>
        <Routes>
          <Route path="/editor/:projectId/:sceneId" element={<EditorPage />} />
        </Routes>
      </MemoryRouter>
    );

    // 等待错误消息显示
    await waitFor(() => {
      expect(screen.getByTestId('engine-error')).toBeInTheDocument();
      expect(screen.getByText('引擎初始化失败')).toBeInTheDocument();
    });
  });
});

// 测试数据验证
describe('数据验证测试', () => {
  it('应该验证输入数据', () => {
    // 创建一个带有表单验证的组件
    const TestForm = () => {
      const [error, setError] = React.useState<string | null>(null);
      
      const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const form = e.target as HTMLFormElement;
        const nameInput = form.elements.namedItem('name') as HTMLInputElement;
        
        if (!nameInput.value.trim()) {
          setError('名称不能为空');
          return;
        }
        
        setError(null);
      };
      
      return (
        <form onSubmit={handleSubmit} data-testid="test-form">
          <input name="name" data-testid="name-input" />
          <button type="submit">提交</button>
          {error && <div data-testid="error-message">{error}</div>}
        </form>
      );
    };
    
    render(<TestForm />);
    
    // 提交空表单
    fireEvent.click(screen.getByText('提交'));
    
    // 验证错误消息
    expect(screen.getByTestId('error-message')).toBeInTheDocument();
    expect(screen.getByText('名称不能为空')).toBeInTheDocument();
    
    // 输入有效数据
    fireEvent.change(screen.getByTestId('name-input'), { target: { value: '测试名称' } });
    fireEvent.click(screen.getByText('提交'));
    
    // 验证错误消息已清除
    expect(screen.queryByTestId('error-message')).not.toBeInTheDocument();
  });
});

// 测试异常恢复
describe('异常恢复测试', () => {
  it('应该能够从错误中恢复', async () => {
    // 创建一个可以从错误中恢复的组件
    const RecoverableComponent = () => {
      const [hasError, setHasError] = React.useState(false);
      const [isRecovered, setIsRecovered] = React.useState(false);
      
      const causeError = () => {
        setHasError(true);
      };
      
      const recover = () => {
        setHasError(false);
        setIsRecovered(true);
      };
      
      if (hasError) {
        return (
          <div>
            <div data-testid="error-state">发生错误</div>
            <button onClick={recover}>恢复</button>
          </div>
        );
      }
      
      return (
        <div>
          {isRecovered ? (
            <div data-testid="recovered-state">已恢复</div>
          ) : (
            <div data-testid="normal-state">正常状态</div>
          )}
          <button onClick={causeError}>触发错误</button>
        </div>
      );
    };
    
    render(<RecoverableComponent />);
    
    // 验证初始状态
    expect(screen.getByTestId('normal-state')).toBeInTheDocument();
    
    // 触发错误
    fireEvent.click(screen.getByText('触发错误'));
    
    // 验证错误状态
    expect(screen.getByTestId('error-state')).toBeInTheDocument();
    
    // 恢复
    fireEvent.click(screen.getByText('恢复'));
    
    // 验证恢复状态
    expect(screen.getByTestId('recovered-state')).toBeInTheDocument();
  });
});
