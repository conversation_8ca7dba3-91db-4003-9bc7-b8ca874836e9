/**
 * 动画重定向编辑器面板
 * 用于集成到编辑器中
 */
import React, { useState, useEffect, useRef } from 'react';
import { Layout, Button, Select, Upload, message, Space, Spin, Modal } from 'antd';
import {
  UploadOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CloseOutlined,
  ImportOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
// 移除引擎直接导入
import RetargetingEditor from './RetargetingEditor';
import './AnimationEditor.less';

const { Header, Content } = Layout;
const { Option } = Select;

/**
 * 重定向编辑器面板属性
 */
interface RetargetingEditorPanelProps {
  /** 是否可见 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 保存回调 */
  onSave?: (data: RetargetingResult) => void;
  /** 初始数据 */
  initialData?: RetargetingResult;
}

/**
 * 重定向结果
 */
export interface RetargetingResult {
  /** 骨骼映射 */
  boneMapping: BoneMapping[];
  /** 重定向配置 */
  config: RetargetingConfig;
  /** 源模型 */
  sourceModel?: THREE.Object3D;
  /** 目标模型 */
  targetModel?: THREE.Object3D;
  /** 源动画片段 */
  sourceClip?: THREE.AnimationClip;
  /** 重定向后的动画片段 */
  retargetedClip?: THREE.AnimationClip;
}

/**
 * 动画重定向编辑器面板组件
 */
const RetargetingEditorPanel: React.FC<RetargetingEditorPanelProps> = ({
  visible,
  onClose,
  onSave,
  initialData,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [sourceModel, setSourceModel] = useState<THREE.Object3D | null>(null);
  const [targetModel, setTargetModel] = useState<THREE.Object3D | null>(null);
  const [sourceSkeleton, setSourceSkeleton] = useState<THREE.Skeleton | null>(null);
  const [targetSkeleton, setTargetSkeleton] = useState<THREE.Skeleton | null>(null);
  const [sourceClip, setSourceClip] = useState<THREE.AnimationClip | null>(null);
  const [sourceClips, setSourceClips] = useState<THREE.AnimationClip[]>([]);
  const [selectedClipName, setSelectedClipName] = useState<string>('');
  const [boneMapping, setBoneMapping] = useState<BoneMapping[]>([]);
  const [config, setConfig] = useState<RetargetingConfig>({
    boneMapping: [],
    preservePositionTracks: true,
    preserveScaleTracks: false,
    normalizeRotations: true,
    adjustRootHeight: true,
    adjustBoneLength: true,
  });
  const [isPlaying, setIsPlaying] = useState(false);
  const [retargetedClip, setRetargetedClip] = useState<THREE.AnimationClip | null>(null);
  const previewContainerRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const animationMixerRef = useRef<THREE.AnimationMixer | null>(null);
  const clockRef = useRef<THREE.Clock | null>(null);
  const animationRef = useRef<number | null>(null);

  // 初始化
  useEffect(() => {
    if (visible) {
      if (initialData) {
        // 加载初始数据
        setBoneMapping(initialData.boneMapping || []);
        setConfig(initialData.config || config);
        if (initialData.sourceModel) setSourceModel(initialData.sourceModel);
        if (initialData.targetModel) setTargetModel(initialData.targetModel);
        if (initialData.sourceClip) {
          setSourceClip(initialData.sourceClip);
          setSourceClips([initialData.sourceClip]);
          setSelectedClipName(initialData.sourceClip.name);
        }
        if (initialData.retargetedClip) setRetargetedClip(initialData.retargetedClip);
      }

      // 初始化预览
      if (previewContainerRef.current) {
        initPreview();
      }
    } else {
      // 清理预览
      cleanupPreview();
    }

    return () => {
      cleanupPreview();
    };
  }, [visible, initialData]);

  // 初始化预览
  const initPreview = () => {
    if (!previewContainerRef.current) return;

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x1e1e1e);
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(
      50,
      previewContainerRef.current.clientWidth / previewContainerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 1.5, 3);
    camera.lookAt(0, 1, 0);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(previewContainerRef.current.clientWidth, previewContainerRef.current.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.shadowMap.enabled = true;
    previewContainerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // 添加灯光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // 添加网格
    const gridHelper = new THREE.GridHelper(10, 10);
    scene.add(gridHelper);

    // 创建时钟
    clockRef.current = new THREE.Clock();

    // 添加模型
    if (sourceModel) scene.add(sourceModel.clone());
    if (targetModel) scene.add(targetModel.clone());

    // 开始动画循环
    animate();
  };

  // 动画循环
  const animate = () => {
    if (!rendererRef.current || !sceneRef.current || !cameraRef.current) return;

    animationRef.current = requestAnimationFrame(animate);

    // 更新动画混合器
    if (animationMixerRef.current && clockRef.current && isPlaying) {
      const delta = clockRef.current.getDelta();
      animationMixerRef.current.update(delta);
    }

    // 渲染场景
    rendererRef.current.render(sceneRef.current, cameraRef.current);
  };

  // 清理预览
  const cleanupPreview = () => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = null;
    }

    if (rendererRef.current && previewContainerRef.current) {
      previewContainerRef.current.removeChild(rendererRef.current.domElement);
      rendererRef.current.dispose();
      rendererRef.current = null;
    }

    if (animationMixerRef.current) {
      animationMixerRef.current = null;
    }

    sceneRef.current = null;
    cameraRef.current = null;
    clockRef.current = null;
  };

  // 加载源模型
  const handleLoadSourceModel = (file: File) => {
    setLoading(true);
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target?.result) {
        setLoading(false);
        return;
      }

      const extension = file.name.split('.').pop()?.toLowerCase();
      
      if (extension === 'glb' || extension === 'gltf') {
        loadGLTFModel(event.target.result as ArrayBuffer, true);
      } else if (extension === 'fbx') {
        loadFBXModel(event.target.result as ArrayBuffer, true);
      } else {
        message.error(t('editor.animation.retargeting.unsupportedFormat'));
        setLoading(false);
      }
    };

    reader.readAsArrayBuffer(file);
    return false; // 阻止默认上传行为
  };

  // 加载目标模型
  const handleLoadTargetModel = (file: File) => {
    setLoading(true);
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target?.result) {
        setLoading(false);
        return;
      }

      const extension = file.name.split('.').pop()?.toLowerCase();
      
      if (extension === 'glb' || extension === 'gltf') {
        loadGLTFModel(event.target.result as ArrayBuffer, false);
      } else if (extension === 'fbx') {
        loadFBXModel(event.target.result as ArrayBuffer, false);
      } else {
        message.error(t('editor.animation.retargeting.unsupportedFormat'));
        setLoading(false);
      }
    };

    reader.readAsArrayBuffer(file);
    return false; // 阻止默认上传行为
  };

  // 加载GLTF模型
  const loadGLTFModel = (buffer: ArrayBuffer, isSource: boolean) => {
    const loader = new GLTFLoader();
    
    loader.parse(buffer, '', (gltf) => {
      const model = gltf.scene;
      
      // 查找骨骼网格
      let skinnedMesh: THREE.SkinnedMesh | null = null;
      model.traverse((object) => {
        if (object instanceof THREE.SkinnedMesh) {
          skinnedMesh = object;
        }
      });

      if (!skinnedMesh) {
        message.error(t('editor.animation.retargeting.noSkinnedMesh'));
        setLoading(false);
        return;
      }

      if (isSource) {
        setSourceModel(model);
        setSourceSkeleton(skinnedMesh.skeleton);
        setSourceClips(gltf.animations);
        
        if (gltf.animations.length > 0) {
          setSourceClip(gltf.animations[0]);
          setSelectedClipName(gltf.animations[0].name);
        }

        message.success(t('editor.animation.retargeting.sourceModelLoaded'));
      } else {
        setTargetModel(model);
        setTargetSkeleton(skinnedMesh.skeleton);
        message.success(t('editor.animation.retargeting.targetModelLoaded'));
      }

      // 更新预览
      updatePreview();
      setLoading(false);
    }, (error) => {
      console.error('加载GLTF模型失败:', error);
      message.error(t('editor.animation.retargeting.loadModelFailed'));
      setLoading(false);
    });
  };

  // 加载FBX模型
  const loadFBXModel = (buffer: ArrayBuffer, isSource: boolean) => {
    const loader = new FBXLoader();
    
    // 将ArrayBuffer转换为URL
    const blob = new Blob([buffer]);
    const url = URL.createObjectURL(blob);
    
    loader.load(url, (fbx) => {
      // 查找骨骼网格
      let skinnedMesh: THREE.SkinnedMesh | null = null;
      fbx.traverse((object) => {
        if (object instanceof THREE.SkinnedMesh) {
          skinnedMesh = object;
        }
      });

      if (!skinnedMesh) {
        message.error(t('editor.animation.retargeting.noSkinnedMesh'));
        setLoading(false);
        URL.revokeObjectURL(url);
        return;
      }

      if (isSource) {
        setSourceModel(fbx);
        setSourceSkeleton(skinnedMesh.skeleton);
        
        // 获取动画片段
        const animations = fbx.animations;
        setSourceClips(animations);
        
        if (animations.length > 0) {
          setSourceClip(animations[0]);
          setSelectedClipName(animations[0].name);
        }

        message.success(t('editor.animation.retargeting.sourceModelLoaded'));
      } else {
        setTargetModel(fbx);
        setTargetSkeleton(skinnedMesh.skeleton);
        message.success(t('editor.animation.retargeting.targetModelLoaded'));
      }

      // 更新预览
      updatePreview();
      setLoading(false);
      URL.revokeObjectURL(url);
    }, undefined, (error) => {
      console.error('加载FBX模型失败:', error);
      message.error(t('editor.animation.retargeting.loadModelFailed'));
      setLoading(false);
      URL.revokeObjectURL(url);
    });
  };

  // 更新预览
  const updatePreview = () => {
    if (!sceneRef.current) return;

    // 清除现有模型
    const modelsToRemove: THREE.Object3D[] = [];
    sceneRef.current.traverse((object) => {
      if (object instanceof THREE.SkinnedMesh) {
        modelsToRemove.push(object.parent!);
      }
    });

    modelsToRemove.forEach((model) => {
      sceneRef.current!.remove(model);
    });

    // 添加源模型
    if (sourceModel) {
      const sourceModelClone = sourceModel.clone();
      sourceModelClone.position.set(-1, 0, 0);
      sceneRef.current.add(sourceModelClone);

      // 创建动画混合器
      if (sourceClip) {
        const mixer = new THREE.AnimationMixer(sourceModelClone);
        const action = mixer.clipAction(sourceClip);
        action.play();
        animationMixerRef.current = mixer;
      }
    }

    // 添加目标模型
    if (targetModel) {
      const targetModelClone = targetModel.clone();
      targetModelClone.position.set(1, 0, 0);
      sceneRef.current.add(targetModelClone);

      // 如果有重定向后的动画，应用到目标模型
      if (retargetedClip && targetModelClone) {
        const mixer = new THREE.AnimationMixer(targetModelClone);
        const action = mixer.clipAction(retargetedClip);
        action.play();
        
        // 如果已有混合器，则替换
        if (animationMixerRef.current) {
          const oldMixer = animationMixerRef.current;
          animationMixerRef.current = new THREE.AnimationMixer(null);
          
          // 合并两个混合器的更新
          const originalUpdate = animationMixerRef.current.update;
          animationMixerRef.current.update = (delta: number) => {
            originalUpdate.call(animationMixerRef.current, delta);
            oldMixer.update(delta);
            mixer.update(delta);
          };
        } else {
          animationMixerRef.current = mixer;
        }
      }
    }
  };

  // 选择动画片段
  const handleSelectClip = (clipName: string) => {
    const clip = sourceClips.find(c => c.name === clipName);
    if (clip) {
      setSourceClip(clip);
      setSelectedClipName(clipName);
      updatePreview();
    }
  };

  // 切换播放状态
  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  // 更新骨骼映射和配置
  const handleUpdate = (newBoneMapping: BoneMapping[], newConfig: RetargetingConfig) => {
    setBoneMapping(newBoneMapping);
    setConfig(newConfig);
  };

  // 重定向动画
  const handleRetarget = () => {
    if (!sourceClip || !sourceSkeleton || !targetSkeleton) {
      message.warning(t('editor.animation.retargeting.missingData'));
      return;
    }

    if (boneMapping.length === 0) {
      message.warning(t('editor.animation.retargeting.noBoneMapping'));
      return;
    }

    try {
      // 这里应该调用引擎的重定向功能
      // 由于我们无法直接调用引擎，这里只是模拟
      // 实际项目中应该使用 AnimationRetargeting.retargetClip
      const retargetedClipMock = sourceClip.clone();
      retargetedClipMock.name = `${sourceClip.name}_retargeted`;
      
      setRetargetedClip(retargetedClipMock);
      message.success(t('editor.animation.retargeting.retargetSuccess'));
      
      // 更新预览
      updatePreview();
    } catch (error) {
      console.error('重定向动画失败:', error);
      message.error(t('editor.animation.retargeting.retargetFailed'));
    }
  };

  // 保存结果
  const handleSave = () => {
    if (!onSave) return;

    const result: RetargetingResult = {
      boneMapping,
      config,
      sourceModel: sourceModel || undefined,
      targetModel: targetModel || undefined,
      sourceClip: sourceClip || undefined,
      retargetedClip: retargetedClip || undefined,
    };

    onSave(result);
    onClose();
  };

  // 导出骨骼映射
  const handleExportMapping = () => {
    if (boneMapping.length === 0) {
      message.warning(t('editor.animation.retargeting.noBoneMapping'));
      return;
    }

    const data = JSON.stringify({ boneMapping, config }, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'bone_mapping.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 导入骨骼映射
  const handleImportMapping = (file: File) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target?.result) return;

      try {
        const data = JSON.parse(event.target.result as string);
        
        if (data.boneMapping && Array.isArray(data.boneMapping)) {
          setBoneMapping(data.boneMapping);
          
          if (data.config) {
            setConfig(data.config);
          }
          
          message.success(t('editor.animation.retargeting.importMappingSuccess'));
        } else {
          message.error(t('editor.animation.retargeting.invalidMappingFile'));
        }
      } catch (error) {
        console.error('导入骨骼映射失败:', error);
        message.error(t('editor.animation.retargeting.importMappingFailed'));
      }
    };

    reader.readAsText(file);
    return false; // 阻止默认上传行为
  };

  return (
    <Layout className="retargeting-editor-panel" style={{ display: visible ? 'flex' : 'none' }}>
      <Header className="retargeting-editor-header">
        <div className="retargeting-editor-title">{t('editor.animation.retargeting.title')}</div>
        <div className="retargeting-editor-controls">
          <Space>
            <Button
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={togglePlay}
              disabled={!sourceClip}
            />
            <Button
              type="primary"
              onClick={handleRetarget}
              disabled={!sourceClip || !sourceSkeleton || !targetSkeleton || boneMapping.length === 0}
            >
              {t('editor.animation.retargeting.retarget')}
            </Button>
            <Button icon={<SaveOutlined />} type="primary" onClick={handleSave}>
              {t('editor.animation.retargeting.save')}
            </Button>
            <Button icon={<CloseOutlined />} onClick={onClose}>
              {t('editor.animation.retargeting.close')}
            </Button>
          </Space>
        </div>
      </Header>

      <Content className="retargeting-editor-content">
        <Spin spinning={loading} tip={t('editor.animation.retargeting.loading')}>
          <div className="retargeting-editor-main">
            <div className="retargeting-editor-controls">
              <div className="model-controls">
                <div className="control-group">
                  <div className="control-label">{t('editor.animation.retargeting.sourceModel')}</div>
                  <Upload
                    beforeUpload={handleLoadSourceModel}
                    showUploadList={false}
                    accept=".glb,.gltf,.fbx"
                  >
                    <Button icon={<UploadOutlined />}>
                      {sourceModel ? t('editor.animation.retargeting.changeSourceModel') : t('editor.animation.retargeting.loadSourceModel')}
                    </Button>
                  </Upload>
                </div>

                <div className="control-group">
                  <div className="control-label">{t('editor.animation.retargeting.targetModel')}</div>
                  <Upload
                    beforeUpload={handleLoadTargetModel}
                    showUploadList={false}
                    accept=".glb,.gltf,.fbx"
                  >
                    <Button icon={<UploadOutlined />}>
                      {targetModel ? t('editor.animation.retargeting.changeTargetModel') : t('editor.animation.retargeting.loadTargetModel')}
                    </Button>
                  </Upload>
                </div>

                {sourceClips.length > 0 && (
                  <div className="control-group">
                    <div className="control-label">{t('editor.animation.retargeting.animation')}</div>
                    <Select
                      style={{ width: '100%' }}
                      value={selectedClipName}
                      onChange={handleSelectClip}
                    >
                      {sourceClips.map(clip => (
                        <Option key={clip.name} value={clip.name}>{clip.name}</Option>
                      ))}
                    </Select>
                  </div>
                )}

                <div className="control-group">
                  <Space>
                    <Upload
                      beforeUpload={handleImportMapping}
                      showUploadList={false}
                      accept=".json"
                    >
                      <Button icon={<ImportOutlined />}>
                        {t('editor.animation.retargeting.importMapping')}
                      </Button>
                    </Upload>
                    <Button
                      icon={<ExportOutlined />}
                      onClick={handleExportMapping}
                      disabled={boneMapping.length === 0}
                    >
                      {t('editor.animation.retargeting.exportMapping')}
                    </Button>
                  </Space>
                </div>
              </div>

              <div className="preview-view" ref={previewContainerRef} />

              <div className="mapping-editor">
                <RetargetingEditor
                  sourceSkeleton={sourceSkeleton || undefined}
                  targetSkeleton={targetSkeleton || undefined}
                  boneMapping={boneMapping}
                  config={config}
                  onUpdate={handleUpdate}
                  onPreview={updatePreview}
                  showPreview={false}
                />
              </div>
            </div>
          </div>
        </Spin>
      </Content>
    </Layout>
  );
};

export default RetargetingEditorPanel;
