/**
 * 角色控制器编辑器组件
 * 用于编辑角色控制器属性
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Switch, InputNumber, Button, Collapse, Tooltip, Space, Divider } from 'antd';
import { InfoCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
// 移除实体导入
import { Vector3Input } from '../common/Vector3Input';

const { Option } = Select;
const { Panel } = Collapse;

interface CharacterControllerEditorProps {
  entityId: string;
}

/**
 * 角色控制器编辑器组件
 */
const CharacterControllerEditor: React.FC<CharacterControllerEditorProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 暂时使用模拟数据
  const entity = {
    id: entityId,
    components: {
      characterController: {
        offset: 0.01,
        maxSlopeClimbAngle: (60 * Math.PI) / 180,
        minSlopeSlideAngle: (30 * Math.PI) / 180,
        autoStep: {
          maxHeight: 0.5,
          minWidth: 0.1,
          stepOverDynamic: true
        },
        enableSnapToGround: 0.1
      }
    }
  };

  // 获取角色控制器组件数据
  const characterControllerComponent = entity?.components?.characterController;
  
  // 表单状态
  const [form] = Form.useForm();
  
  // 初始化表单
  useEffect(() => {
    if (characterControllerComponent) {
      form.setFieldsValue({
        offset: characterControllerComponent.offset || 0.01,
        maxSlopeClimbAngle: characterControllerComponent.maxSlopeClimbAngle || 60,
        minSlopeSlideAngle: characterControllerComponent.minSlopeSlideAngle || 30,
        enableAutoStep: characterControllerComponent.autoStep ? true : false,
        autoStepMaxHeight: characterControllerComponent.autoStep?.maxHeight || 0.5,
        autoStepMinWidth: characterControllerComponent.autoStep?.minWidth || 0.1,
        autoStepOverDynamic: characterControllerComponent.autoStep?.stepOverDynamic || true,
        enableSnapToGround: characterControllerComponent.enableSnapToGround ? true : false,
        snapToGroundDistance: typeof characterControllerComponent.enableSnapToGround === 'number' 
          ? characterControllerComponent.enableSnapToGround 
          : 0.1
      });
    }
  }, [characterControllerComponent, form]);
  
  // 处理表单变更
  const handleValuesChange = (changedValues: any, allValues: any) => {
    if (!entity) return;
    
    // 构建角色控制器选项
    const options: any = {
      offset: allValues.offset,
      maxSlopeClimbAngle: (allValues.maxSlopeClimbAngle * Math.PI) / 180, // 转换为弧度
      minSlopeSlideAngle: (allValues.minSlopeSlideAngle * Math.PI) / 180, // 转换为弧度
    };
    
    // 自动台阶设置
    if (allValues.enableAutoStep) {
      options.autoStep = {
        maxHeight: allValues.autoStepMaxHeight,
        minWidth: allValues.autoStepMinWidth,
        stepOverDynamic: allValues.autoStepOverDynamic
      };
    }
    
    // 地面吸附设置
    if (allValues.enableSnapToGround) {
      options.enableSnapToGround = allValues.snapToGroundDistance;
    } else {
      options.enableSnapToGround = false;
    }
    
    // 暂时只在控制台输出变更
    console.log('Character controller values changed:', options);
  };
  
  // 处理移除组件
  const handleRemoveComponent = () => {
    if (!entity) return;

    // 暂时只在控制台输出移除操作
    console.log('Remove character controller component for entity:', entityId);
  };
  
  // 如果没有实体或角色控制器组件，显示空状态
  if (!entity || !characterControllerComponent) {
    return (
      <div className="component-editor empty-state">
        <p>{t('editor.physics.noCharacterController')}</p>
        <Button
          type="primary"
          onClick={() => {
            console.log('Add character controller component for entity:', entityId);
          }}
        >
          {t('editor.physics.addCharacterController')}
        </Button>
      </div>
    );
  }
  
  return (
    <div className="component-editor character-controller-editor">
      <div className="component-header">
        <h3>{t('editor.physics.characterController')}</h3>
        <Tooltip title={t('editor.common.remove')}>
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={handleRemoveComponent}
          />
        </Tooltip>
      </div>
      
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        <Form.Item 
          name="offset" 
          label={t('editor.physics.offset')}
          tooltip={t('editor.physics.characterOffsetTooltip')}
          rules={[{ type: 'number', min: 0.001 }]}
        >
          <InputNumber min={0.001} step={0.01} style={{ width: '100%' }} />
        </Form.Item>
        
        <Collapse defaultActiveKey={['slopes', 'autoStep', 'snapToGround']}>
          <Panel header={t('editor.physics.slopeSettings')} key="slopes">
            <Form.Item 
              name="maxSlopeClimbAngle" 
              label={t('editor.physics.maxSlopeClimbAngle')}
              tooltip={t('editor.physics.maxSlopeClimbAngleTooltip')}
              rules={[{ type: 'number', min: 0, max: 90 }]}
            >
              <InputNumber min={0} max={90} step={1} style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item 
              name="minSlopeSlideAngle" 
              label={t('editor.physics.minSlopeSlideAngle')}
              tooltip={t('editor.physics.minSlopeSlideAngleTooltip')}
              rules={[{ type: 'number', min: 0, max: 90 }]}
            >
              <InputNumber min={0} max={90} step={1} style={{ width: '100%' }} />
            </Form.Item>
          </Panel>
          
          <Panel header={t('editor.physics.autoStepSettings')} key="autoStep">
            <Form.Item 
              name="enableAutoStep" 
              label={t('editor.physics.enableAutoStep')}
              tooltip={t('editor.physics.enableAutoStepTooltip')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            
            <Form.Item 
              name="autoStepMaxHeight" 
              label={t('editor.physics.autoStepMaxHeight')}
              tooltip={t('editor.physics.autoStepMaxHeightTooltip')}
              rules={[{ type: 'number', min: 0.01 }]}
            >
              <InputNumber min={0.01} step={0.1} style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item 
              name="autoStepMinWidth" 
              label={t('editor.physics.autoStepMinWidth')}
              tooltip={t('editor.physics.autoStepMinWidthTooltip')}
              rules={[{ type: 'number', min: 0.01 }]}
            >
              <InputNumber min={0.01} step={0.01} style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item 
              name="autoStepOverDynamic" 
              label={t('editor.physics.autoStepOverDynamic')}
              tooltip={t('editor.physics.autoStepOverDynamicTooltip')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Panel>
          
          <Panel header={t('editor.physics.snapToGroundSettings')} key="snapToGround">
            <Form.Item 
              name="enableSnapToGround" 
              label={t('editor.physics.enableSnapToGround')}
              tooltip={t('editor.physics.enableSnapToGroundTooltip')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            
            <Form.Item 
              name="snapToGroundDistance" 
              label={t('editor.physics.snapToGroundDistance')}
              tooltip={t('editor.physics.snapToGroundDistanceTooltip')}
              rules={[{ type: 'number', min: 0.01 }]}
            >
              <InputNumber min={0.01} step={0.01} style={{ width: '100%' }} />
            </Form.Item>
          </Panel>
        </Collapse>
      </Form>
    </div>
  );
};

export default CharacterControllerEditor;
