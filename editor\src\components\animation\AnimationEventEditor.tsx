/**
 * 动画事件编辑器
 */
import React from 'react';
import { Card, Form, InputNumber, Switch, Select, Input, Button, Space, Typography, message } from 'antd';
import { DeleteOutlined, PlayCircleOutlined } from '@ant-design/icons';

const { Option } = Select;
const { Text } = Typography;
const { TextArea } = Input;

/**
 * 动画事件编辑器属性
 */
interface AnimationEventEditorProps {
  /** 事件 */
  events: Record<string, any>;
  /** 选中的事件名称 */
  selectedEvent: string | null;
  /** 事件更新回调 */
  onEventUpdate: (name: string, event: any) => void;
  /** 事件删除回调 */
  onEventDelete: (name: string) => void;
  /** 事件测试回调 */
  onEventTest?: (name: string) => void;
}

/**
 * 动画事件编辑器
 */
export const AnimationEventEditor: React.FC<AnimationEventEditorProps> = ({
  events,
  selectedEvent,
  onEventUpdate,
  onEventDelete,
  onEventTest
}) => {
  const [form] = Form.useForm();

  // 当前选中的事件
  const currentEvent = selectedEvent ? events[selectedEvent] : null;

  // 更新事件属性
  const handleEventChange = (field: string, value: any) => {
    if (selectedEvent && currentEvent) {
      const updatedEvent = {
        ...currentEvent,
        [field]: value
      };
      onEventUpdate(selectedEvent, updatedEvent);
    }
  };

  // 测试事件
  const handleTestEvent = () => {
    if (selectedEvent && onEventTest) {
      onEventTest(selectedEvent);
      message.success('事件测试已触发');
    }
  };

  // 删除事件
  const handleDeleteEvent = () => {
    if (selectedEvent) {
      onEventDelete(selectedEvent);
      message.success('事件已删除');
    }
  };

  // 如果没有选中事件，显示提示
  if (!selectedEvent || !currentEvent) {
    return (
      <Card title="动画事件编辑器" size="small">
        <Text type="secondary">请选择一个事件进行编辑</Text>
      </Card>
    );
  }

  return (
    <Card 
      title="动画事件编辑器" 
      size="small"
      extra={
        <Space>
          <Button 
            icon={<PlayCircleOutlined />} 
            size="small" 
            onClick={handleTestEvent}
          >
            测试
          </Button>
          <Button 
            icon={<DeleteOutlined />} 
            size="small" 
            danger 
            onClick={handleDeleteEvent}
          >
            删除
          </Button>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={currentEvent}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleEventChange(field, value);
          });
        }}
      >
        <Form.Item label="名称" name="name">
          <Input
            value={currentEvent.name}
            onChange={(e) => handleEventChange('name', e.target.value)}
          />
        </Form.Item>

        <Form.Item label="触发类型" name="triggerType">
          <Select
            value={currentEvent.triggerType || 'time'}
            onChange={(value) => handleEventChange('triggerType', value)}
          >
            <Option value="time">时间触发</Option>
            <Option value="frame">帧触发</Option>
            <Option value="marker">标记触发</Option>
            <Option value="percentage">百分比触发</Option>
          </Select>
        </Form.Item>

        <Form.Item 
          label={
            currentEvent.triggerType === 'time' ? '触发时间 (秒)' :
            currentEvent.triggerType === 'frame' ? '触发帧' :
            currentEvent.triggerType === 'percentage' ? '触发百分比 (0-100)' :
            '触发值'
          } 
          name="triggerValue"
        >
          <InputNumber
            min={0}
            max={currentEvent.triggerType === 'percentage' ? 100 : undefined}
            step={currentEvent.triggerType === 'time' ? 0.1 : 1}
            value={currentEvent.triggerValue || 0}
            onChange={(value) => handleEventChange('triggerValue', value || 0)}
          />
        </Form.Item>

        <Form.Item label="启用" name="enabled" valuePropName="checked">
          <Switch
            checked={currentEvent.enabled !== false}
            onChange={(checked) => handleEventChange('enabled', checked)}
          />
        </Form.Item>

        <Form.Item label="只触发一次" name="once" valuePropName="checked">
          <Switch
            checked={currentEvent.once || false}
            onChange={(checked) => handleEventChange('once', checked)}
          />
        </Form.Item>

        <Form.Item label="事件类型" name="eventType">
          <Select
            value={currentEvent.eventType || 'custom'}
            onChange={(value) => handleEventChange('eventType', value)}
          >
            <Option value="custom">自定义</Option>
            <Option value="sound">播放声音</Option>
            <Option value="particle">播放粒子</Option>
            <Option value="camera">摄像机事件</Option>
            <Option value="ui">UI事件</Option>
            <Option value="script">脚本事件</Option>
          </Select>
        </Form.Item>

        <Form.Item label="事件参数" name="parameters">
          <TextArea
            rows={4}
            value={currentEvent.parameters ? JSON.stringify(currentEvent.parameters, null, 2) : '{}'}
            onChange={(e) => {
              try {
                const params = JSON.parse(e.target.value);
                handleEventChange('parameters', params);
              } catch (error) {
                // 忽略JSON解析错误，用户可能正在输入
              }
            }}
            placeholder="输入JSON格式的事件参数..."
          />
        </Form.Item>

        <Form.Item label="描述" name="description">
          <TextArea
            rows={2}
            value={currentEvent.description || ''}
            onChange={(e) => handleEventChange('description', e.target.value)}
            placeholder="输入事件描述..."
          />
        </Form.Item>
      </Form>

      <div style={{ marginTop: 16 }}>
        <Text strong>事件信息:</Text>
        <div style={{ marginTop: 8 }}>
          <Text>名称: {currentEvent.name}</Text><br />
          <Text>触发类型: {currentEvent.triggerType || 'time'}</Text><br />
          <Text>触发值: {currentEvent.triggerValue || 0}</Text><br />
          <Text>启用: {currentEvent.enabled !== false ? '是' : '否'}</Text><br />
          <Text>只触发一次: {currentEvent.once ? '是' : '否'}</Text>
        </div>
      </div>
    </Card>
  );
};

export default AnimationEventEditor;
