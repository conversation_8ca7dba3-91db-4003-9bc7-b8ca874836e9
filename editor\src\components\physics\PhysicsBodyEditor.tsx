/**
 * 物理体编辑器组件
 * 用于编辑物理体属性
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Switch, InputNumber, Button, Collapse, Tooltip, Space, Divider } from 'antd';
import { InfoCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
// 暂时移除 entities slice 导入，使用 editor slice
// 移除引擎导入，使用本地类型定义
import { Vector3Input } from '../common/Vector3Input';

const { Option } = Select;
const { Panel } = Collapse;

interface PhysicsBodyEditorProps {
  entityId: string;
}

/**
 * 物理体编辑器组件
 */
const PhysicsBodyEditor: React.FC<PhysicsBodyEditorProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 暂时使用模拟数据
  const entity = {
    id: entityId,
    components: {
      physicsBody: {
        type: 'dynamic',
        mass: 1,
        linearDamping: 0.01,
        angularDamping: 0.01,
        fixedRotation: false,
        allowSleep: true,
        sleepSpeedLimit: 0.1,
        sleepTimeLimit: 1,
        linearFactor: { x: 1, y: 1, z: 1 },
        angularFactor: { x: 1, y: 1, z: 1 },
        material: 'default'
      }
    }
  };

  // 获取物理体组件数据
  const physicsBodyComponent = entity?.components?.physicsBody;
  
  // 表单状态
  const [form] = Form.useForm();
  
  // 初始化表单
  useEffect(() => {
    if (physicsBodyComponent) {
      form.setFieldsValue({
        type: physicsBodyComponent.type || 'dynamic',
        mass: physicsBodyComponent.mass || 1,
        linearDamping: physicsBodyComponent.linearDamping || 0.01,
        angularDamping: physicsBodyComponent.angularDamping || 0.01,
        fixedRotation: physicsBodyComponent.fixedRotation || false,
        allowSleep: physicsBodyComponent.allowSleep || true,
        sleepSpeedLimit: physicsBodyComponent.sleepSpeedLimit || 0.1,
        sleepTimeLimit: physicsBodyComponent.sleepTimeLimit || 1,
        linearFactor: physicsBodyComponent.linearFactor || { x: 1, y: 1, z: 1 },
        angularFactor: physicsBodyComponent.angularFactor || { x: 1, y: 1, z: 1 },
        material: physicsBodyComponent.material || 'default'
      });
    }
  }, [physicsBodyComponent, form]);
  
  // 处理表单变更
  const handleValuesChange = (changedValues: any, allValues: any) => {
    if (!entity) return;

    // 暂时只在控制台输出变更
    console.log('Physics body values changed:', changedValues);
  };
  
  // 处理移除组件
  const handleRemoveComponent = () => {
    if (!entity) return;

    // 暂时只在控制台输出移除操作
    console.log('Remove physics body component for entity:', entityId);
  };
  
  // 如果没有实体或物理体组件，显示空状态
  if (!entity || !physicsBodyComponent) {
    return (
      <div className="component-editor empty-state">
        <p>{t('editor.physics.noPhysicsBody')}</p>
        <Button
          type="primary"
          onClick={() => {
            console.log('Add physics body component for entity:', entityId);
          }}
        >
          {t('editor.physics.addPhysicsBody')}
        </Button>
      </div>
    );
  }
  
  return (
    <div className="component-editor physics-body-editor">
      <div className="component-header">
        <h3>{t('editor.physics.physicsBody')}</h3>
        <Tooltip title={t('editor.common.remove')}>
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={handleRemoveComponent}
          />
        </Tooltip>
      </div>
      
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
        initialValues={{
          type: physicsBodyComponent.type || 'dynamic',
          mass: physicsBodyComponent.mass || 1,
          linearDamping: physicsBodyComponent.linearDamping || 0.01,
          angularDamping: physicsBodyComponent.angularDamping || 0.01,
          fixedRotation: physicsBodyComponent.fixedRotation || false,
          allowSleep: physicsBodyComponent.allowSleep || true,
          sleepSpeedLimit: physicsBodyComponent.sleepSpeedLimit || 0.1,
          sleepTimeLimit: physicsBodyComponent.sleepTimeLimit || 1,
          linearFactor: physicsBodyComponent.linearFactor || { x: 1, y: 1, z: 1 },
          angularFactor: physicsBodyComponent.angularFactor || { x: 1, y: 1, z: 1 },
          material: physicsBodyComponent.material || 'default'
        }}
      >
        <Form.Item 
          name="type" 
          label={t('editor.physics.type')}
          tooltip={t('editor.physics.typeTooltip')}
        >
          <Select>
            <Option value="static">{t('editor.physics.static')}</Option>
            <Option value="dynamic">{t('editor.physics.dynamic')}</Option>
            <Option value="kinematic">{t('editor.physics.kinematic')}</Option>
          </Select>
        </Form.Item>
        
        <Form.Item 
          name="mass" 
          label={t('editor.physics.mass')}
          tooltip={t('editor.physics.massTooltip')}
          rules={[{ type: 'number', min: 0 }]}
        >
          <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
        </Form.Item>
        
        <Collapse defaultActiveKey={['damping']}>
          <Panel header={t('editor.physics.damping')} key="damping">
            <Form.Item 
              name="linearDamping" 
              label={t('editor.physics.linearDamping')}
              tooltip={t('editor.physics.linearDampingTooltip')}
              rules={[{ type: 'number', min: 0, max: 1 }]}
            >
              <InputNumber min={0} max={1} step={0.01} style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item 
              name="angularDamping" 
              label={t('editor.physics.angularDamping')}
              tooltip={t('editor.physics.angularDampingTooltip')}
              rules={[{ type: 'number', min: 0, max: 1 }]}
            >
              <InputNumber min={0} max={1} step={0.01} style={{ width: '100%' }} />
            </Form.Item>
          </Panel>
          
          <Panel header={t('editor.physics.constraints')} key="constraints">
            <Form.Item 
              name="fixedRotation" 
              label={t('editor.physics.fixedRotation')}
              tooltip={t('editor.physics.fixedRotationTooltip')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            
            <Form.Item 
              name="linearFactor" 
              label={t('editor.physics.linearFactor')}
              tooltip={t('editor.physics.linearFactorTooltip')}
            >
              <Vector3Input />
            </Form.Item>
            
            <Form.Item 
              name="angularFactor" 
              label={t('editor.physics.angularFactor')}
              tooltip={t('editor.physics.angularFactorTooltip')}
            >
              <Vector3Input />
            </Form.Item>
          </Panel>
          
          <Panel header={t('editor.physics.sleep')} key="sleep">
            <Form.Item 
              name="allowSleep" 
              label={t('editor.physics.allowSleep')}
              tooltip={t('editor.physics.allowSleepTooltip')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            
            <Form.Item 
              name="sleepSpeedLimit" 
              label={t('editor.physics.sleepSpeedLimit')}
              tooltip={t('editor.physics.sleepSpeedLimitTooltip')}
              rules={[{ type: 'number', min: 0 }]}
            >
              <InputNumber min={0} step={0.01} style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item 
              name="sleepTimeLimit" 
              label={t('editor.physics.sleepTimeLimit')}
              tooltip={t('editor.physics.sleepTimeLimitTooltip')}
              rules={[{ type: 'number', min: 0 }]}
            >
              <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
            </Form.Item>
          </Panel>
          
          <Panel header={t('editor.physics.material')} key="material">
            <Form.Item 
              name="material" 
              label={t('editor.physics.materialType')}
              tooltip={t('editor.physics.materialTypeTooltip')}
            >
              <Select>
                <Option value="default">{t('editor.physics.materials.default')}</Option>
                <Option value="wood">{t('editor.physics.materials.wood')}</Option>
                <Option value="metal">{t('editor.physics.materials.metal')}</Option>
                <Option value="plastic">{t('editor.physics.materials.plastic')}</Option>
                <Option value="rubber">{t('editor.physics.materials.rubber')}</Option>
                <Option value="ice">{t('editor.physics.materials.ice')}</Option>
                <Option value="glass">{t('editor.physics.materials.glass')}</Option>
                <Option value="concrete">{t('editor.physics.materials.concrete')}</Option>
                <Option value="custom">{t('editor.physics.materials.custom')}</Option>
              </Select>
            </Form.Item>
          </Panel>
        </Collapse>
      </Form>
    </div>
  );
};

export default PhysicsBodyEditor;
