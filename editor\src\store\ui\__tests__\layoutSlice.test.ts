/**
 * 布局状态切片测试
 */
import layoutReducer, {
  setLayout,
  saveLayout,
  loadLayout,
  deleteLayout,
  resetLayout,
  toggleTheme,
  saveLayoutToStorage,
  loadLayoutFromStorage,
  defaultLayout,
  predefinedLayouts,
} from '../layoutSlice';
import { jest } from '@jest/globals';

// 模拟localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('布局状态切片', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.clear();
  });

  // 初始状态测试
  it('应该返回初始状态', () => {
    const initialState = layoutReducer(undefined, { type: 'unknown' });
    expect(initialState).toEqual({
      layout: null,
      savedLayouts: predefinedLayouts,
      activeLayout: 'default',
      theme: 'light',
    });
  });

  // 设置布局测试
  it('应该处理setLayout', () => {
    const mockLayout = {
      dockbox: {
        mode: 'horizontal',
        children: [
          {
            tabs: [
              { id: 'test', title: '测试', content: 'test-content' }
            ]
          }
        ]
      }
    };
    const state = layoutReducer(undefined, setLayout(mockLayout));
    expect(state.layout).toEqual(mockLayout);
  });

  // 保存布局测试
  it('应该处理saveLayout', () => {
    const mockLayout = {
      dockbox: {
        mode: 'horizontal',
        children: [
          {
            tabs: [
              { id: 'test', title: '测试', content: 'test-content' }
            ]
          }
        ]
      }
    };
    const state = layoutReducer(undefined, saveLayout({ name: 'test-layout', layout: mockLayout }));
    expect(state.savedLayouts['test-layout']).toEqual(mockLayout);
    expect(state.activeLayout).toBe('test-layout');
  });

  // 加载布局测试
  it('应该处理loadLayout', () => {
    // 先保存一个布局
    let state = layoutReducer(undefined, saveLayout({ 
      name: 'test-layout', 
      layout: { test: 'layout' } as any 
    }));
    
    // 然后加载这个布局
    state = layoutReducer(state, loadLayout('test-layout'));
    expect(state.layout).toEqual({ test: 'layout' });
    expect(state.activeLayout).toBe('test-layout');
  });

  // 删除布局测试
  it('应该处理deleteLayout', () => {
    // 先保存一个布局
    let state = layoutReducer(undefined, saveLayout({ 
      name: 'test-layout', 
      layout: { test: 'layout' } as any 
    }));
    
    // 设置为当前活动布局
    state = layoutReducer(state, loadLayout('test-layout'));
    
    // 然后删除这个布局
    state = layoutReducer(state, deleteLayout('test-layout'));
    expect(state.savedLayouts['test-layout']).toBeUndefined();
    expect(state.activeLayout).toBe('default');
    expect(state.layout).toEqual(defaultLayout);
  });

  // 不应该删除默认布局
  it('不应该删除默认布局', () => {
    const state = layoutReducer(undefined, deleteLayout('default'));
    expect(state.savedLayouts['default']).toEqual(defaultLayout);
  });

  // 重置布局测试
  it('应该处理resetLayout', () => {
    // 先设置一个自定义布局
    let state = layoutReducer(undefined, setLayout({ test: 'custom-layout' } as any));
    
    // 然后重置布局
    state = layoutReducer(state, resetLayout());
    expect(state.layout).toEqual(defaultLayout);
    expect(state.activeLayout).toBe('default');
    
    // 验证localStorage调用
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'dl-engine-editor-layout',
      JSON.stringify(defaultLayout)
    );
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'dl-engine-editor-active-layout',
      'default'
    );
  });

  // 切换主题测试
  it('应该处理toggleTheme', () => {
    // 默认是亮色主题
    let state = layoutReducer(undefined, { type: 'unknown' });
    expect(state.theme).toBe('light');
    
    // 切换到暗色主题
    state = layoutReducer(state, toggleTheme());
    expect(state.theme).toBe('dark');
    
    // 再次切换回亮色主题
    state = layoutReducer(state, toggleTheme());
    expect(state.theme).toBe('light');
  });

  // 保存布局到本地存储测试
  it('应该处理saveLayoutToStorage', () => {
    const mockLayout = { test: 'layout' } as any;
    const state = layoutReducer(undefined, saveLayoutToStorage(mockLayout));
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'dl-engine-editor-layout',
      JSON.stringify(mockLayout)
    );
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'dl-engine-editor-saved-layouts',
      JSON.stringify(state.savedLayouts)
    );
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'dl-engine-editor-active-layout',
      state.activeLayout
    );
  });

  // 从本地存储加载布局测试
  it('应该处理loadLayoutFromStorage', () => {
    // 模拟localStorage中有保存的布局
    const mockLayout = { test: 'saved-layout' };
    const mockSavedLayouts = { 
      default: defaultLayout,
      custom: { test: 'custom-layout' }
    };
    const mockActiveLayout = 'custom';
    
    localStorageMock.setItem('dl-engine-editor-layout', JSON.stringify(mockLayout));
    localStorageMock.setItem('dl-engine-editor-saved-layouts', JSON.stringify(mockSavedLayouts));
    localStorageMock.setItem('dl-engine-editor-active-layout', mockActiveLayout);
    
    const state = layoutReducer(undefined, loadLayoutFromStorage());
    
    expect(state.layout).toEqual(mockLayout);
    expect(state.savedLayouts).toEqual(mockSavedLayouts);
    expect(state.activeLayout).toBe(mockActiveLayout);
  });

  // 处理从本地存储加载布局失败的情况
  it('应该在loadLayoutFromStorage失败时使用默认布局', () => {
    // 模拟localStorage.getItem抛出异常
    localStorageMock.getItem.mockImplementation(() => {
      throw new Error('模拟存储错误');
    });
    
    const state = layoutReducer(undefined, loadLayoutFromStorage());
    
    expect(state.layout).toEqual(defaultLayout);
  });
});
